#===========================================================
# Shared Variables for US-West-2 in Production Account
#===========================================================

variable "vpc_name" {
  description = "VPC Name"
  default     = "awsaaia3"
}

variable "rr_vpc_name" {
  description = "Rates & Residuals VPC Name"
  default     = "awsaaia"
}

variable "availability_zones" {
  description = "Available Availability Zones for this Region"
  type        = list(string)
  default     = ["us-west-2a", "us-west-2b", "us-west-2c"]
}

variable "nfs_cidr" {
  description = "The CIDR block of the internal aws networks for NFS usage."
  default     = "***********/22"
}

variable "certificate_arn" {
  description = "SSL Cert to use for consumer webstack"
  default     = "arn:aws:acm:us-west-2:************:certificate/454cca1b-eefa-48fd-a8e3-a082d13275c1"
}

