/* Setup the security group for the ELB(s) in the target environment. */
resource "aws_security_group" "sg-elb-ais10-internal-webstack" {
  name        = "${var.application}-internal-${var.environment}-${var.country_iso_code}"
  description = "Manages ELB for 1.0 Internal Web Stack - ${var.country_iso_code}"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow all inbound HTTP traffic
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow all inbound HTTPS traffic
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow SSH from in-network
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.homenet_cidr, var.remote_cidr, var.ais_cidr]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Application    = var.application
    Environment    = var.environment
    Service        = var.service
    Release        = var.build_number
    LaunchedBy     = var.launched_by
    LaunchedOn     = var.launched_on
    SlackContact   = var.slack_contact
    Name           = "sg-elb-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.country_iso_code}-${var.build_number}"
    Group          = "WEB"
    CountryIsoCode = var.country_iso_code
    Component      = var.component
  }
}

# Create a new load balancer
resource "aws_elb" "ais10_internal-webstack-elb" {
  name            = "${var.application}-internal-${var.environment}-${var.country_iso_code}"
  security_groups = [aws_security_group.sg-elb-ais10-internal-webstack.id]
  subnets         = var.private_subnet_ids

  internal = true

  /* ##########################################################################################
  Remember to add a bucket
  access_logs {
    bucket        = "${var.elb_log_bucket}"
    bucket_prefix = "${var.application}"
    interval      = 5
    enabled       = false
  }
########################################################################################## */

  listener {
    instance_port     = 80
    instance_protocol = "http"
    lb_port           = 80
    lb_protocol       = "http"
  }

  health_check {
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 3
    target              = "HTTP:80${var.health_check_path}"
    interval            = 30
  }

  cross_zone_load_balancing   = true
  idle_timeout                = 600
  connection_draining         = true
  connection_draining_timeout = 400

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Application    = var.application
    Environment    = var.environment
    Service        = var.service
    Release        = var.build_number
    LaunchedBy     = var.launched_by
    LaunchedOn     = var.launched_on
    SlackContact   = var.slack_contact
    Name           = "elb-${var.region_abbreviated}-${var.application_abbreviated}-${var.component}-${var.environment}-${var.country_iso_code}-${var.build_number}"
    Group          = "WEB"
    CountryIsoCode = var.country_iso_code
    Component      = var.component
  }
}

