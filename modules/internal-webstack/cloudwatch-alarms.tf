############################################
# Apache Instances Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "apache-instance-warning" {
  alarm_name          = "Internal_Webstack_UnixMetrics_ApacheBusyInstances_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2"
  metric_name         = "ApacheBusyInstances"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "100"
  alarm_description = "Warning threshold for Apache instances in ${var.environment} Internal Webstack"
  actions_enabled   = false

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# Apache Instances Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "apache-instance-critical" {
  alarm_name          = "Internal_Webstack_UnixMetrics_ApacheBusyInstances_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2"
  metric_name         = "ApacheBusyInstances"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "200"
  alarm_description = "Critical threshold for Apache instances in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

############################################
# CPU Load 1 minute period Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "cpu-one-minute-load-warning" {
  alarm_name          = "Internal_Webstack_UnixMetrics_LoadAverage1minute_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "LoadAverage1minute"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "5"
  alarm_description = "Warning threshold for CPU Load Average 1 minute interval in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# CPU Load 1 minute period Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "cpu-one-minute-load-critical" {
  alarm_name          = "Internal_Webstack_UnixMetrics_LoadAverage1minute_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "LoadAverage1minute"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "9"
  alarm_description = "Critical threshold for CPU Load Average 1 minute interval in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

############################################
# CPU Load 5 minute period Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "cpu-five-minute-load-warning" {
  alarm_name          = "Internal_Webstack_UnixMetrics_LoadAverage5minute_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "LoadAverage5minute"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "5"
  alarm_description = "Warning threshold for CPU Load Average 5 minute interval in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# CPU Load 5 minute period Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "cpu-five-minute-load-critical" {
  alarm_name          = "Internal_Webstack_UnixMetrics_LoadAverage5minute_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "LoadAverage5minute"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "9"
  alarm_description = "Critical threshold for CPU Load Average 5 minute interval in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

############################################
# CPU Load 15 minute period Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "cpu-ten-minute-load-warning" {
  alarm_name          = "Internal_Webstack_UnixMetrics_LoadAverage15minute_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "LoadAverage15minute"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "5"
  alarm_description = "Warning threshold for CPU Load Average 10 minute interval in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# CPU Load 15 minute period Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "cpu-ten-minute-load-critical" {
  alarm_name          = "Internal_Webstack_UnixMetrics_LoadAverage15minute_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "LoadAverage15minute"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "9"
  alarm_description = "Critical threshold for CPU Load Average 10 minute interval in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

############################################
# Disk Usage Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "disk-usage-warning" {
  alarm_name          = "Internal_Webstack_UnixMetrics_DiskPecentageUsed_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "DiskPecentageUsed"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "80"
  alarm_description = "Warning threshold for Disk Usage in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# Disk Usage Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "disk-usage-critical" {
  alarm_name          = "Internal_Webstack_UnixMetrics_DiskPecentageUsed_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "DiskPecentageUsed"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "90"
  alarm_description = "Critical threshold for Disk Usage in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

############################################
# Memory Usage Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "memory-usage-warning" {
  alarm_name          = "Internal_Webstack_UnixMetrics_PercentageMemoryUsed_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "PercentageMemoryUsed"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "80"
  alarm_description = "Warning threshold for Memory Usage in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# Memory Usage Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "memory-usage-critical" {
  alarm_name          = "Internal_Webstack_UnixMetrics_PercentageMemoryUsed_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "PercentageMemoryUsed"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "90"
  alarm_description = "Critical threshold for Memory Usage in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

############################################
# Swap Memory Usage Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "swap-memory-usage-warning" {
  alarm_name          = "Internal_Webstack_UnixMetrics_PercentageSwapMemoryUsed_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "PercentageSwapMemoryUsed"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "90"
  alarm_description = "Warning threshold for Swap Memory Usage in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# Swap Memory Usage Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "swap-memory-usage-critical" {
  alarm_name          = "Internal_Webstack_UnixMetrics_PercentageSwapMemoryUsed_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "PercentageSwapMemoryUsed"
  namespace           = var.metric_namespace

  # 1 minute in seconds
  period            = "60"
  statistic         = "Maximum"
  threshold         = "95"
  alarm_description = "Critical threshold for Swap Memory Usage in ${var.environment} Internal Webstack"

  dimensions = {
    Environment = var.environment
  }
  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

