###############################################################
# Pass-through Variables
###############################################################

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "component" {
}

variable "component_id" {
}

variable "region" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "homenet_cidr" {
}

variable "ais_cidr" {
}

variable "remote_cidr" {
}

variable "nfs_cidr" {
}

variable "package_bucket_name" {
}

variable "country_iso_code" {
}

variable "vpc_id" {
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "availability_zones" {
  type = list(string)
}

###############################################################
# ELB/EC2 Variables
###############################################################

variable "internal_hosted_zone_id" {
  description = "AWS Id of the ais-internal hosted zone"
}

variable "elb_route53_name" {
  description = "Name that should be used for the ELB Route53 record"
}

variable "elb_log_bucket" {
  description = "S3 bucket to store the elb logs"
  default     = ""
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  default     = "AIS10"
}

variable "health_check_path" {
  description = "path from the root of the web server to the health check page. i.e. /newsystem/healthcheck.php"
  default     = "/health-check.php"
}

variable "asg_ami" {
  description = "AMI ID for the 1.0 webstack AMI pre-configured with the LAMP stack, code, and ini deployment"
}

variable "webstack_instance_type" {
  description = "Instance side to use for 1.0 internal webstack"
  default     = "m5.large"
}

variable "efs_security_group" {
  description = "The security group that has access to the efs"
}

###############################################################
# AutoScale Variables
###############################################################

variable "asg_min_size" {
  description = "Default min number of EC2s in autoscale group"
  default     = "1"
}

variable "asg_max_size" {
  description = "Default max number of EC2s in autoscale group"
  default     = "1"
}

variable "asg_desired_capacity" {
  description = "Default desired number of EC2s in autoscale group"
  default     = "1"
}

variable "asg_min_elb_capacity" {
  description = "Default autoscale elb capacity"
  default     = "1"
}

variable "asg_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for normal business hours"
}

variable "asg_scheduling_normal_map" {
  description = "Autoscaling scheduler map for normal business hour scale-up/down"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

variable "asg_extended_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for extended (change-day) business hours"
}

variable "asg_scheduling_extended_map" {
  description = "Autoscaling scheduler map for extended business hour scale-up/down on first/second of the month"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

###############################################################
# Cloudwatch Alarm Variables
###############################################################

variable "warning_alert_arn" {
  description = "ARN of the warning alert SNS topic"
}

variable "critical_alert_arn" {
  description = "ARN of the critical alert SNS topic"
}

variable "metric_namespace" {
  description = "Namespace of the Internal Webstack Unix metrics in cloudwatch"
  default     = "Unix-Metrics-Internal-Webstack"
}

