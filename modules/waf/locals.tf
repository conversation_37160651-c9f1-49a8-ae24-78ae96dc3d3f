locals {
  scope                 = "REGIONAL"
  rule_prefix           = "AIS-CAI-ERSManagedRules"
  short_name            = "AIS-CAI-ERSManagedRules"
  std_allowlist_comment = "Provided by CAI Production Security Engineering. Allows Qualys, Whitehat, Veracode, etc."
  std_denylist_comment  = "Provided by CAI Production Security Engineering. Disllowed IP ranges."
  qualys_v4_ips         = ["**********/20", "************/23"]
  qualys_v6_ips         = ["2602:FDAA:0:2108::/64", "2600:0C02:1020:2881::/64", "2600:C08:2015:4400::/64", "2600:0C02:1020:2111::/64", "2600:0C02:1020:2224::/64"]
  white_hat_non_eu_ips  = ["************/32", "************/32", "************/32", "************/32", "************/32", "*************/27", "***********/32", "***********/32", "***********/32", "***********/32", "***********/32", "***********/32", "***********/32", "***********/32"]
  white_hat_eu_ips      = ["*************/32", "*************/32", "**************/32", "**************/32"]
  veracode_ips          = ["**************/32"]
  epc_ips               = ["*************/32", "*************/32"]
  allowlist_v4_ips      = concat(local.qualys_v4_ips, local.white_hat_non_eu_ips, local.white_hat_eu_ips, local.veracode_ips, local.epc_ips)
  allowlist_v6_ips      = local.qualys_v6_ips
}