
resource "aws_cloudwatch_log_group" "aws_waf_logs" {
  name              = "aws-waf-logs-${var.application}-${var.environment}"
  retention_in_days = 7
}

resource "aws_wafv2_web_acl_logging_configuration" "aws_waf_enable_cloudwatch_logs" {
  log_destination_configs = [aws_cloudwatch_log_group.aws_waf_logs.arn]
  resource_arn            = aws_wafv2_web_acl.webacl_linux.arn
  depends_on = [
    aws_wafv2_web_acl.webacl_linux,
    aws_cloudwatch_log_group.aws_waf_logs
  ]
}

resource "aws_cloudwatch_metric_alarm" "waf_request_count_alarm" {
  alarm_name                = "aws-waf-${var.application}-${var.environment}-request-count-threshold-alarm"
  alarm_description         = "This metric monitors request count of waf"
  metric_name               = "CountedRequests"
  namespace                 = "AWS/WAFV2"
  period                    = 300
  evaluation_periods        = 2
  statistic                 = "Sum"
  threshold                 = var.limit
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  dimensions = {
    Rule      =   "${local.rule_prefix}-rate-limit-rule-metrics"
    WebACL    =   aws_wafv2_web_acl.webacl_linux.name
    Region    =   var.region
  }
  alarm_actions = [var.critical_alert_arn]
}
