############################################
# Elasticache current connections alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "current-connections-warning" {
  alarm_name          = "Elasticache_ConnectionCount_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "CurrConnections"
  namespace           = "AWS/ElastiCache"

  # 1 minute in seconds
  period              = "60"
  statistic           = "Maximum"
  threshold           = "800"
  datapoints_to_alarm = "3"
  alarm_description   = "Warning threshold monitoring for elasticache cluster in ${var.environment}"

  dimensions = {
    CacheClusterId = aws_elasticache_cluster.elasticache_memcached_cluster.cluster_id
  }

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# Elasticache current connections alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "current-connections-critical" {
  alarm_name          = "Elasticache_ConnectionCount_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "CurrConnections"
  namespace           = "AWS/ElastiCache"

  # 1 minute in seconds
  period              = "60"
  statistic           = "Maximum"
  threshold           = "950"
  datapoints_to_alarm = "3"
  alarm_description   = "Critical threshold monitoring for elasticache cluster in ${var.environment}"

  dimensions = {
    CacheClusterId = aws_elasticache_cluster.elasticache_memcached_cluster.cluster_id
  }

  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}





############################################
# Memcached utilization alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "utilization-warning" {
  alarm_name          = "Elasticache_Utilization_Warning_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ElastiCache"

  # 1 minute in seconds
  period              = "60"
  statistic           = "Maximum"
  threshold           = "80"
  datapoints_to_alarm = "3"
  alarm_description   = "Warning threshold monitoring for elasticache cluster in ${var.environment}"

  dimensions = {
    CacheClusterId = aws_elasticache_cluster.elasticache_memcached_cluster.cluster_id
  }

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]
}

############################################
# Elasticache utilization alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "utilization-critical" {
  alarm_name          = "Elasticache_Utilization_Critical_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ElastiCache"

  # 1 minute in seconds
  period              = "60"
  statistic           = "Maximum"
  threshold           = "90"
  datapoints_to_alarm = "3"
  alarm_description   = "Critical threshold monitoring for elasticache cluster in ${var.environment}"

  dimensions = {
    CacheClusterId = aws_elasticache_cluster.elasticache_memcached_cluster.cluster_id
  }

  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]
}

