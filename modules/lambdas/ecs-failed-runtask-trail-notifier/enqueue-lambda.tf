resource "aws_lambda_function" "enqueue" {
  filename         = var.enqueue_lambda_package_path
  function_name    = "${var.application}-${var.environment}-ecs-failed-runtask-trail-enqueue"
  description      = "Receives CloudTrail events for ECS RunTask actions and stores failures in queue"
  role             = var.lambda_role_arn[var.account_type]
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  source_code_hash = filebase64sha256(var.enqueue_lambda_package_path)
  memory_size      = 128
  timeout          = 120

  environment {
    variables = {
      Region      = var.region
      Environment = var.environment
      SqsQueueUrl = aws_sqs_queue.failed_runtask_events.id
    }
  }

  lifecycle {
    ignore_changes = [tags.LaunchedOn]
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Release      = var.build_number
    Component    = var.component
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "lm-${var.region_abbreviated}-${var.application_abbreviated}-${var.environment}-ecs-failed-runtask-trail-enqueue"
  }
}

resource "aws_lambda_permission" "enqueue" {
  statement_id  = "${aws_cloudwatch_event_rule.enqueue_lambda.name}_InvokePermission"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.enqueue.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.enqueue_lambda.arn
}

resource "aws_cloudwatch_event_rule" "enqueue_lambda" {
  name        = "${var.application}-${var.environment}-ecs-failed-runtask-trail-enqueue"
  description = "Forwards CloudTrail events for ECS RunTask actions to monitor for failures"
  is_enabled  = var.account_type == "nonprod" ? false : true

  lifecycle {
    ignore_changes = [is_enabled]
  }

  event_pattern = <<PATTERN
{
  "source": [
    "aws.ecs"
  ],
  "detail-type": [
    "AWS API Call via CloudTrail"
  ],
  "detail": {
    "eventSource": [
      "ecs.amazonaws.com"
    ],
    "eventName": [
      "RunTask"
    ]
  }
}
PATTERN

}

resource "aws_cloudwatch_event_target" "enqueue_lambda" {
  rule = aws_cloudwatch_event_rule.enqueue_lambda.name
  arn  = aws_lambda_function.enqueue.arn
}

