resource "aws_lambda_function" "notify" {
  filename         = var.notify_lambda_package_path
  function_name    = "${var.application}-${var.environment}-ecs-failed-runtask-trail-notify"
  description      = "Processes transformed CloudTrail events for failed ECS RunTask actions from queue and fires Slack notification"
  role             = var.lambda_role_arn[var.account_type]
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  source_code_hash = filebase64sha256(var.notify_lambda_package_path)
  memory_size      = 256
  timeout          = 300

  environment {
    variables = {
      Region      = var.region
      Environment = var.environment
      SqsQueueUrl = aws_sqs_queue.failed_runtask_events.id
    }
  }

  lifecycle {
    ignore_changes = [tags.LaunchedOn]
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Release      = var.build_number
    Component    = var.component
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "lm-${var.region_abbreviated}-${var.application_abbreviated}-${var.environment}-ecs-failed-runtask-trail-notify"
  }
}

resource "aws_lambda_permission" "notify" {
  statement_id  = "${aws_cloudwatch_event_rule.notify_lambda.name}_InvokePermission"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.notify.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.notify_lambda.arn
}

resource "aws_cloudwatch_event_rule" "notify_lambda" {
  name                = "${var.application}-${var.environment}-ecs-failed-runtask-trail-notify"
  description         = "Schedule to check queue for failed ECS RunTask actions to fire Slack notification"
  is_enabled          = var.account_type == "nonprod" ? false : true
  schedule_expression = "rate(10 minutes)"

  lifecycle {
    ignore_changes = [is_enabled]
  }
}

resource "aws_cloudwatch_event_target" "notify_lambda" {
  rule = aws_cloudwatch_event_rule.notify_lambda.name
  arn  = aws_lambda_function.notify.arn
}

