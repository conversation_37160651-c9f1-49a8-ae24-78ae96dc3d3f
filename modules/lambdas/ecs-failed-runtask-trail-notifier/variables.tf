###############################################################
# Pass-through Variables
###############################################################

variable "account_type" {
}

variable "account_name" {
}

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "region" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "component" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

###############################################################
# Lambda Variables
###############################################################

variable "enqueue_lambda_package_path" {
  description = "The path to the packaged enqueue lambda zip to be uploaded"
}

variable "notify_lambda_package_path" {
  description = "The path to the packaged notify lambda zip to be uploaded"
}

variable "lambda_role_arn" {
  description = "Role that both lambdas use to execute as"
  type        = map(string)

  default = {
    "nonprod" = "arn:aws:iam::************:role/acct-managed/ais10-ecs-failed-runtask-trail-lambda-role"
    "prod"    = "arn:aws:iam::********4985:role/acct-managed/ais10-ecs-failed-runtask-trail-lambda-role"
  }
}

