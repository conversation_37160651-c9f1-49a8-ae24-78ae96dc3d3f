# ecs-failed-runtask-trail-notifier module  

Due to technical issues (CloudTrail filtering limitations, CAI deny policies, etc), we're unable to have the ideal scenario of a single lambda that handles bulk notifications of 
ECS tasks that fail on the RunTask action according to CloudTrail. Instead we broke it up into two lambdas with a queue; the "enqueue lambda" stores the actual events of interest 
in the queue, and the "notify lambda" reads several events at once for Slack notifications. The modules within this `lambda` sub-directory were meant to only be one-off lambdas, but 
despite having two lambdas plus a queue, it still feels okay to keep it here since it's a small independent set of resources acting as one.