###############################################################
# Pass-through Variables
###############################################################

variable "account_type" {
}

variable "account_name" {
}

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "region" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "component" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

###############################################################
# Lambda Variables
###############################################################

variable "lambda_package_path" {
  description = "The path to the packaged lambda zip that should be uploaded"
}

variable "lambda_role_arn" {
  description = "Role that the lambda should run under"
  type        = map(string)

  default = {
    "nonprod" = "arn:aws:iam::************:role/acct-managed/ais10-ecs-task-state-forwarder-lambda-role"
    "prod"    = "arn:aws:iam::************:role/acct-managed/ais10-ecs-task-state-forwarder-lambda-role"
  }
}

