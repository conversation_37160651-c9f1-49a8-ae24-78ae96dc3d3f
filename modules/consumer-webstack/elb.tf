/* Setup the security group for the ELB(s) in the target environment. */
resource "aws_security_group" "sg-elb-ais10-webstack" {
  name        = "${var.application}-${var.component}-${var.environment}-elb"
  description = "Manages ELB for 1.0 Web Stack"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow all inbound HTTP traffic
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow all inbound HTTPS traffic
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow SSH from in-network
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.homenet_cidr, var.remote_cidr, var.ais_cidr]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "sg-elb-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
    Group        = "WEB"
    Component    = var.component
  }
}

#Application load balancer

resource "aws_lb" "ais10_consumer_webstack_alb" {
  name            = "${var.application}-consumer-${var.environment}-alb"
  load_balancer_type = "application"
  security_groups = [aws_security_group.sg-elb-ais10-webstack.id]
  subnets         = var.public_subnet_ids
  idle_timeout                = 400
  internal                    = false

  lifecycle {
    create_before_destroy = true
  }

  access_logs {
    bucket        = var.alb_logs_bucket
    prefix        = "${var.application}-${var.component}/${var.environment}"
    enabled       = var.alb_logs_enabled
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "alb-${var.region_abbreviated}-${var.application_abbreviated}-${var.component}-${var.environment}-${var.build_number}"
    Group        = "WEB"
    Component    = var.component
  }
}

resource "aws_lb_listener" "ais10_elb_listen443" {
  load_balancer_arn = aws_lb.ais10_consumer_webstack_alb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.ais10_alb_tg.arn
  }
}

resource "aws_lb_listener" "ais10_elb_listen80" {
  load_balancer_arn = aws_lb.ais10_consumer_webstack_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}


resource "aws_lb_target_group" "ais10_alb_tg" {
  name     = "${var.application}-${var.environment}-alb-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = var.vpc_id

  deregistration_delay = 400

  stickiness {
    type            = "lb_cookie"
    enabled         = false
  }

  health_check {
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 3
    interval            = 30
    path                = var.health_check_path
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "lbtg-${var.region_abbreviated}-${var.application_abbreviated}-${var.component}-${var.environment}-${var.build_number}"
    Group        = "WEB"
    Component    = var.component
  }
}

resource "aws_wafv2_web_acl_association" "web_acl_linux" {
  resource_arn = aws_lb.ais10_consumer_webstack_alb.arn
  web_acl_arn  = var.waf_acl_arn_linux
}

resource "aws_wafv2_web_acl_association" "web_acl_linux_digiirs" {
  resource_arn = aws_lb.ais10_digirs-elb.arn
  web_acl_arn  = var.waf_acl_arn_linux
}

# Create a new load balancer specifically for Digtal IRS
resource "aws_lb" "ais10_digirs-elb" {
  name               = "${var.application}-digirs-${var.environment}-elb"
  load_balancer_type = "application"
  security_groups    = [aws_security_group.sg-elb-ais10-webstack.id]
  subnets            = var.public_subnet_ids

  access_logs {
    bucket        = var.alb_logs_bucket
    prefix        = "${var.application}-${var.component}-digirs/${var.environment}"
    enabled       = var.alb_logs_enabled
  }

  enable_cross_zone_load_balancing = true
  idle_timeout                     = 400
  internal                         = false

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "elb-${var.region_abbreviated}-${var.application_abbreviated}-${var.component}-${var.environment}-${var.build_number}"
    Group        = "WEB"
    Component    = var.component
  }
}

resource "aws_lb_listener" "ais10_digirs-elb-listen443" {
  load_balancer_arn = aws_lb.ais10_digirs-elb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.ais10_digirs-elb-tg.arn
  }
}

resource "aws_lb_listener" "ais10_digirs-elb-listen80" {
  load_balancer_arn = aws_lb.ais10_digirs-elb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_target_group" "ais10_digirs-elb-tg" {
  name     = "${var.application}-digirs-${var.environment}-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = var.vpc_id

  stickiness {
    type            = "lb_cookie"
    cookie_duration = 14400
    enabled         = true
  }

  health_check {
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 3
    interval            = 30
    path                = var.health_check_path
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "lbtg-${var.region_abbreviated}-${var.application_abbreviated}-${var.component}-${var.environment}-${var.build_number}"
    Group        = "WEB"
    Component    = var.component
  }
}

#DigIRS Autoscaling Attachment
resource "aws_autoscaling_attachment" "svc_asg_external2" {
  alb_target_group_arn   = aws_lb_target_group.ais10_digirs-elb-tg.arn
  autoscaling_group_name = aws_autoscaling_group.ais10-webstack-asg.id
}

# Rest of consumer-webstack attachment
resource "aws_autoscaling_attachment" "svc_consumer_asg_external" {
  alb_target_group_arn   = aws_lb_target_group.ais10_alb_tg.arn
  autoscaling_group_name = aws_autoscaling_group.ais10-webstack-asg.id
}
