#!/bin/bash
yum update -y --exclude=amazon-ssm-agent

# Get instance_id and all needed parts of ais10
instance_id=`curl http://***************/latest/meta-data/instance-id`
echo $instance_id
aws ssm send-command --document-name "GetCodeFromS3" --targets "Key=instanceids,Values=$instance_id" --parameters '{"bucketName":["${package_bucket_name}"], "environment":["${ec2_environment}"], "fileName":["codebasePackage.zip"]}' --timeout-seconds 600 --max-concurrency "33%" --max-errors "0" --region ${region} --output-s3-bucket-name "ais.ssm.output"
aws ssm send-command --document-name "GetInisFromS3AndDoTokenReplacements" --targets "Key=instanceids,Values=$instance_id" --parameters '{"bucketName":["${package_bucket_name}"], "environment":["${ec2_environment}"], "fileName":["iniPackage.zip"], "countryIsoCode":["${country_iso_code}"], "appStackType":["ConsumerWebstack"]}' --timeout-seconds 600 --max-concurrency "33%" --max-errors "0" --region ${region} --output-s3-bucket-name "ais.ssm.output"
aws ssm send-command --document-name "GetServiceConfigsFromS3" --targets "Key=instanceids,Values=$instance_id" --parameters '{"bucketName":["${package_bucket_name}"], "environment":["${ec2_environment}"], "countryIsoCode":["${country_iso_code}"], "appStackType":["ConsumerWebstack"]}' --timeout-seconds 600 --max-concurrency "20%" --max-errors "0" --region ${region} --output-s3-bucket-name "ais.ssm.output"

# store the env variables to a magic linux environment file that makes them available to cron tasks
echo ENVIRONMENT=${ec2_environment}>>/etc/environment
echo INSTANCEID=$instance_id>>/etc/environment
echo REGION=${region}>>/etc/environment
echo MODULE_DIRECTORY="Consumer-Webstack">>/etc/environment
echo country_iso_code=${country_iso_code}>>/etc/environment

############################
# Set crontab for Apache instance bash script to run every minute
#######
(crontab -l ; echo "* * * * * /data/git/AIS-1.0/ShellScripts/ApacheInstanceCount.sh >> /var/log/cron.log 2>&1") | crontab

############################
# Set crontab for Memory Used Percentage bash script to run every minute
#######
(crontab -l ; echo "* * * * * /data/git/AIS-1.0/ShellScripts/MemoryMetrics.sh >> /var/log/cron.log 2>&1") | crontab

############################
# Set crontab for Disk Storage Percentage bash script to run every 5 minutes
#######
(crontab -l ; echo "* * * * * /data/git/AIS-1.0/ShellScripts/DiskStorageMetrics.sh >> /var/log/cron.log 2>&1") | crontab

############################
# Set crontab for CPU Usage Metrics bash script to run every minute
#######
(crontab -l ; echo "* * * * * /data/git/AIS-1.0/ShellScripts/CpuMetrics.sh >> /var/log/cron.log 2>&1") | crontab

