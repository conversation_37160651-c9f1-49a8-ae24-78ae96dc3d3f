resource "aws_route53_record" "advpgm" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-advpgm${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "datadelivery" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-datadelivery${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "briefinline" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-briefinline${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}
resource "aws_route53_record" "dealerdollars" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-dealerdollars${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "digirs" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-digirs${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "autodollars" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-autodollars${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "onecar" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-onecar${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "equengv" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-equengv${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}


resource "aws_route53_record" "status" {
  zone_id = var.consumer_hosted_zone_id
  name    = "v1-status${var.alb_route53_suffix}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = false
  }
}

