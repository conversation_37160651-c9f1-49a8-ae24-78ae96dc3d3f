###############################################################
# Pass-through Variables
###############################################################

variable "account_type" {
}

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "region" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "vpc_id" {
}

variable "vpc_name" {
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "base_ami_id" {
}

variable "user_data_script_name" {
}

variable "efs_id" {
}

###############################################################
# Module Variables
###############################################################

variable "instance_type" {
  description = "Type of EC2 instance to use"
  default     = "m4.large"
}

variable "key_pair_name" {
  description = "Key Pair name for the instance"
  default     = "ais-legacy"
}

variable "instance_profile_role" {
  description = "Role name for the instance profile of the EC2 that the AMI will be created from"
  default     = "ais10-ec2-role"
}

variable "temp_ec2_security_group_ids" {
  description = "Security groups for the EC2 so YUM can connect to repos. Manually created since no way to selectively tear-down once done here."
  type        = map(string)
  default = {
    "nonprod-ue1" = "sg-0f34057ec0bac05d2"
    "prod-ue1"    = "sg-06f8b3f3d607ea6b5"
    "prod-uw2"    = "sg-0109d5a78a363b64e"
  }
}

