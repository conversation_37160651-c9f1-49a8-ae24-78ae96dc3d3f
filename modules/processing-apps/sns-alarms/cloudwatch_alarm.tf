####################################
# DDBv2 long running OEMs US
###################################
resource "aws_cloudwatch_metric_alarm" "us-ddbv2-long-running-oems-alarm" {
  alarm_name          = "US_DDBv2_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv2LogFilter_US_${var.environment}"
  namespace           = "US_DDBv2_Long_RunningLogs"
  period              = "300" # in seconds

  #unit                      = "Count"
  statistic         = "Sum"
  threshold         = var.ddbv2_long_running_oem_warning
  alarm_description = "US Long Running OEMs greater than or equal to ${var.ddbv2_long_running_oem_warning} OEMs"
  alarm_actions     = [var.warning_alert_arn]
  ok_actions        = [var.warning_alert_arn]
}

# patter filter for the Metric Alarm to use.....
resource "aws_cloudwatch_log_metric_filter" "ddbv2-log-filter-warning-us" {
  name           = "DDBv2Log_MetricFilter_US"
  pattern        = "DDBv2LongRunningOEMsDetected"
  log_group_name = "US_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "DDBv2LogFilter_US_${var.environment}"
    namespace = "US_DDBv2_Long_RunningLogs"
    value     = "1"
  }
}

####################################
# DDBv2 long running OEMs CA
###################################
resource "aws_cloudwatch_metric_alarm" "ca-ddbv2-long-running-oems-alarm" {
  alarm_name          = "CA_DDBv2_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv2LogFilter_CA_${var.environment}"
  namespace           = "CA_DDBv2_Long_RunningLogs"
  period              = "300" # in seconds

  #unit                      = "Count"
  statistic         = "Sum"
  threshold         = var.ddbv2_long_running_oem_warning
  alarm_description = "CA Long Running OEMs greater than or equal to ${var.ddbv2_long_running_oem_warning} OEMs"
  alarm_actions     = [var.warning_alert_arn]
  ok_actions        = [var.warning_alert_arn]
}

# patter filter for the Metric Alarm to use.....
resource "aws_cloudwatch_log_metric_filter" "ddbv2-log-filter-warning-ca" {
  name           = "DDBv2Log_MetricFilter_CA"
  pattern        = "DDBv2LongRunningOEMsDetected"
  log_group_name = "CA_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "DDBv2LogFilter_CA_${var.environment}"
    namespace = "CA_DDBv2_Long_RunningLogs"
    value     = "1"
  }
}

####################################
# RRR&I long running OEMs US
###################################
resource "aws_cloudwatch_metric_alarm" "us-rrri-long-running-oems-alarm" {
  alarm_name          = "US_RRRI_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "RRRILogFilter_US_${var.environment}"
  namespace           = "US_RRRI_Long_RunningLogs"
  period              = "300" # in seconds

  #unit                      = "Count"
  statistic         = "Sum"
  threshold         = var.rrri_long_running_oem_warning
  alarm_description = "US Long Running OEMs greater than or equal to ${var.rrri_long_running_oem_warning} OEMs"
  alarm_actions     = [var.rrri_critical_alert_arn]
  ok_actions        = [var.rrri_critical_alert_arn]
}

# patter filter for the Metric Alarm to use.....
resource "aws_cloudwatch_log_metric_filter" "RRRI-log-filter-warning-us" {
  name           = "RRRILog_MetricFilter_US"
  pattern        = "RRRILongRunningOEMsDetected"
  log_group_name = "US_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "RRRILogFilter_US_${var.environment}"
    namespace = "US_RRRI_Long_RunningLogs"
    value     = "1"
  }
}

####################################
# RRRI long running OEMs CA
###################################
resource "aws_cloudwatch_metric_alarm" "ca-RRRI-long-running-oems-alarm" {
  alarm_name          = "CA_RRRI_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "RRRILogFilter_CA_${var.environment}"
  namespace           = "CA_RRRI_Long_RunningLogs"
  period              = "300" # in seconds

  #unit                      = "Count"
  statistic         = "Sum"
  threshold         = var.rrri_long_running_oem_warning
  alarm_description = "CA Long Running OEMs greater than or equal to ${var.rrri_long_running_oem_warning} OEMs"
  alarm_actions     = [var.rrri_critical_alert_arn]
  ok_actions        = [var.rrri_critical_alert_arn]
}

# pattern filter for the Metric Alarm to use.....
resource "aws_cloudwatch_log_metric_filter" "RRRI-log-filter-warning-ca" {
  name           = "RRRILog_MetricFilter_CA"
  pattern        = "RRRILongRunningOEMsDetected"
  log_group_name = "CA_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "RRRILogFilter_CA_${var.environment}"
    namespace = "CA_RRRI_Long_RunningLogs"
    value     = "1"
  }
}

############################################
# US VIN processing check Warning alarms
############################################
resource "aws_cloudwatch_metric_alarm" "us-vin-processing-warning-alarm" {
  alarm_name          = "US_Check_Vin_Process_Warning_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "VinProcessLogWarningFilter_US_${var.environment}"
  namespace           = "US_VIN_Processing_WarningLogs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_warning
  alarm_description   = "Warning, Number US of Pending Vins over ${var.vin_process_alarm_warning}"
  alarm_actions       = [var.warning_alert_arn]
  ok_actions          = [var.warning_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "check-vin-process-log-filter-warning-us" {
  name           = "VinProcessLog_MetricFilter_US"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "US_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogWarningFilter_US_${var.environment}"
    namespace = "US_VIN_Processing_WarningLogs"
    value     = "$number"
  }
}

############################################
# CA VIN processing check Warning alarms
############################################
resource "aws_cloudwatch_metric_alarm" "ca-vin-processing-warning-alarm" {
  alarm_name          = "CA_Check_Vin_Process_Warning_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "VinProcessLogWarningFilter_CA_${var.environment}"
  namespace           = "CA_VIN_Processing_WarningLogs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_warning
  alarm_description   = "Warning, Number CA of Pending Vins over ${var.vin_process_alarm_warning}"
  alarm_actions       = [var.warning_alert_arn]
  ok_actions          = [var.warning_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "check-vin-process-log-filter-warning-ca" {
  name           = "VinProcessLog_MetricFilter_CA"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "CA_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogWarningFilter_CA_${var.environment}"
    namespace = "CA_VIN_Processing_WarningLogs"
    value     = "$number"
  }
}

############################################
# US VIN processing check Critical alarms
############################################
resource "aws_cloudwatch_metric_alarm" "us-vin-processing-critical-alarm" {
  alarm_name          = "US_Check_Vin_Process_Critical_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "VinProcessLogCriticalFilter_US_${var.environment}"
  namespace           = "US_VIN_Processing_CriticalLogs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_critical
  alarm_description   = "Critical, Number US of Pending Vins over ${var.vin_process_alarm_critical}"
  alarm_actions       = [var.critical_alert_arn]
  ok_actions          = [var.critical_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "check-vin-process-log-filter-critical-us" {
  name           = "VinProcessLog_CriticalMetricFilter_US"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "US_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogCriticalFilter_US_${var.environment}"
    namespace = "US_VIN_Processing_CriticalLogs"
    value     = "$number"
  }
}

############################################
# CA VIN processing check Critical alarms
############################################
resource "aws_cloudwatch_metric_alarm" "ca-vin-processing-critical-alarm" {
  alarm_name          = "CA_Check_Vin_Process_Critical_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "VinProcessLogCriticalFilter_CA_${var.environment}"
  namespace           = "CA_VIN_Processing_CriticalLogs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_critical
  alarm_description   = "Critical, Number CA of Pending Vins over ${var.vin_process_alarm_critical}"
  alarm_actions       = [var.critical_alert_arn]
  ok_actions          = [var.critical_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "check-vin-process-log-filter-critical-ca" {
  name           = "VinProcessLog_CriticalMetricFilter_CA"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "CA_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogCriticalFilter_CA_${var.environment}"
    namespace = "CA_VIN_Processing_CriticalLogs"
    value     = "$number"
  }
}

############################################
# DDBv1 US DB Error Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "us-ddbv1-critical-error-alarm" {
  alarm_name          = "us_ddbv1_critical_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv1_Error_Filter_US_${var.environment}"
  namespace           = "DDBv1_Error_Filter_US_Logs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Critical, US DDBv1 has produced and Error"
  alarm_actions       = [var.critical_alert_arn]
  ok_actions          = [var.critical_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "ddbv1_critical_error_log_filter_critical_us" {
  name = "DDBv1_Error_MetricFilter_US"

  #  pattern        = "?ERROR DDB ?DDB.SQL"
  pattern = "?\"ERROR homenet\" ?\"DDB.SQL\""

  log_group_name = "US_DDBv1FileCreation_${var.environment}"

  metric_transformation {
    name      = "DDBv1_Error_Filter_US_${var.environment}"
    namespace = "DDBv1_Error_Filter_US_Logs"
    value     = "1"
  }
}

############################################
# DDBv1 US DB Error Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "us-ddbv1-warning-error-alarm" {
  alarm_name          = "us_ddbv1_warning_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv1_Error_Warning_Filter_US_${var.environment}"
  namespace           = "DDBv1_Error_Warning_Filter_US_Logs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Warning, US DDBv1 has produced and Error"
  alarm_actions       = [var.warning_alert_arn]
  ok_actions          = [var.warning_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "ddbv1_warning_error_log_filter_us" {
  name = "DDBv1_Warning_Error_MetricFilter_US"

  #  pattern        = "?ERROR DDB ?DDB.SQL"
  pattern = "ERROR DDB"

  log_group_name = "US_DDBv1FileCreation_${var.environment}"

  metric_transformation {
    name      = "DDBv1_Error_Warning_Filter_US_${var.environment}"
    namespace = "DDBv1_Error_Warning_Filter_US_Logs"
    value     = "1"
  }
}

############################################
# DDBv2 US DB Error Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "us-ddbv2-critical-error-alarm" {
  alarm_name          = "us_ddbv2_critical_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv2_Error_Filter_US_${var.environment}"
  namespace           = "DDBv2_Error_Filter_US_Logs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Critical, US DDBv2 has produced and Error"
  alarm_actions       = [var.critical_alert_arn]
  ok_actions          = [var.critical_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "ddbv2_critical_error_log_filter_critical_us" {
  name = "DDBv2_Error_MetricFilter_US"

  #  pattern        = "?ERROR DDB ?DDB.SQL"
  pattern = "?\"ERROR homenet\" ?\"DDB.SQL\""

  log_group_name = "US_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_US_${var.environment}"
    namespace = "DDBv2_Error_Filter_US_Logs"
    value     = "1"
  }
}

############################################
# DDBv2 US DB Error Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "us-ddbv2-warning-error-alarm" {
  alarm_name          = "us_ddbv2_warning_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv2_Error_Filter_Warning_US_${var.environment}"
  namespace           = "DDBv2_Error_Filter_Warning_US_Logs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Warning, US DDBv2 has produced and Error"
  alarm_actions       = [var.warning_alert_arn]
  ok_actions          = [var.warning_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "ddbv2_warning_error_log_filter_us" {
  name    = "DDBv2_Error_Warning_MetricFilter_US"
  pattern = "ERROR DDB"

  log_group_name = "US_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_Warning_US_${var.environment}"
    namespace = "DDBv2_Error_Filter_Warning_US_Logs"
    value     = "1"
  }
}

############################################
# DDBv2 CA DB Error Alarm (Critical)
############################################
resource "aws_cloudwatch_metric_alarm" "ca-ddbv2-critical-error-alarm" {
  alarm_name          = "ca_ddbv2_critical_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv2_Error_Filter_CA_${var.environment}"
  namespace           = "DDBv2_Error_Filter_CA_Logs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Critical, CA DDBv2 has produced and Error"
  alarm_actions       = [var.critical_alert_arn]
  ok_actions          = [var.critical_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "ddbv2_critical_error_log_filter_critical_ca" {
  name = "DDBv2_Error_MetricFilter_CA"

  #  pattern        = "?ERROR DDB ?DDB.SQL"
  pattern = "?\"ERROR homenet\" ?\"DDB.SQL\""

  log_group_name = "CA_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_CA_${var.environment}"
    namespace = "DDBv2_Error_Filter_CA_Logs"
    value     = "1"
  }
}

############################################
# DDBv2 CA DB Error Alarm (Warning)
############################################
resource "aws_cloudwatch_metric_alarm" "ca-ddbv2-warning-error-alarm" {
  alarm_name          = "ca_ddbv2_warning_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "DDBv2_Error_Filter_Warning_CA_${var.environment}"
  namespace           = "DDBv2_Error_Filter_Warning_CA_Logs"
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Warning, CA DDBv2 has produced and Error"
  alarm_actions       = [var.warning_alert_arn]
  ok_actions          = [var.warning_alert_arn]
}

resource "aws_cloudwatch_log_metric_filter" "ddbv2_warning_error_log_filter_ca" {
  name    = "DDBv2_Error_Warning_MetricFilter_CA"
  pattern = "ERROR DDB"

  log_group_name = "CA_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_Warning_CA_${var.environment}"
    namespace = "DDBv2_Error_Filter_Warning_CA_Logs"
    value     = "1"
  }
}

/* cloudwatch metrics and alamr for ftp DDBv2 small cloudwatch log pattern */

resource "aws_cloudwatch_log_metric_filter" "FTP-retry-exceeded-DDBv2-small" {
  name           = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-small"
  pattern        = "\"File retry count 5 for file\""
  log_group_name = "DDBv2ProcessDivision_small_${var.environment}"

  metric_transformation {
    name      = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-small"
    namespace = "ProcessingApps"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "FTP_retry_exceeded_DDBv2_small" {
  alarm_name          = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-small"
  namespace           = "ProcessingApps"
  alarm_description   = "Triggered when FTP metrics reaches >= ${var.cw_alarm_ftp_threshold} for ${var.cw_alarm_ftp_evaluation_periods} period(s) of ${var.cw_alarm_ftp_periods} seconds."
  metric_name         = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-small"
  statistic           = "Sum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.cw_alarm_ftp_threshold
  period              = var.cw_alarm_ftp_periods
  evaluation_periods  = var.cw_alarm_ftp_evaluation_periods
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.critical_alert_arn]
}

/* cloudwatch metrics and alamr for ftp DDBv2 medium cloudwatch log pattern */

resource "aws_cloudwatch_log_metric_filter" "FTP-retry-exceeded-DDBv2-medium" {
  name           = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-medium"
  pattern        = "\"File retry count 5 for file\""
  log_group_name = "DDBv2ProcessDivision_medium_${var.environment}"

  metric_transformation {
    name      = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-medium"
    namespace = "ProcessingApps"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "FTP_retry_exceeded_DDBv2_medium" {
  alarm_name          = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-medium"
  namespace           = "ProcessingApps"
  alarm_description   = "Triggered when FTP metrics reaches >= ${var.cw_alarm_ftp_threshold} for ${var.cw_alarm_ftp_evaluation_periods} period(s) of ${var.cw_alarm_ftp_periods} seconds."
  metric_name         = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-medium"
  statistic           = "Sum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.cw_alarm_ftp_threshold
  period              = var.cw_alarm_ftp_periods
  evaluation_periods  = var.cw_alarm_ftp_evaluation_periods
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.critical_alert_arn]
}

/* cloudwatch metrics and alamr for ftp DDBv2 large cloudwatch log pattern */

resource "aws_cloudwatch_log_metric_filter" "FTP-retry-exceeded-DDBv2-large" {
  name           = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-large"
  pattern        = "\"File retry count 5 for file\""
  log_group_name = "DDBv2ProcessDivision_large_${var.environment}"

  metric_transformation {
    name      = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-large"
    namespace = "ProcessingApps"
    value     = "1"
  }
}

/* High FTP retry limit exceeded */
resource "aws_cloudwatch_metric_alarm" "FTP_retry_exceeded_DDBv2_large" {
  alarm_name          = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-large"
  namespace           = "ProcessingApps"
  alarm_description   = "Triggered when FTP metrics reaches >= ${var.cw_alarm_ftp_threshold} for ${var.cw_alarm_ftp_evaluation_periods} period(s) of ${var.cw_alarm_ftp_periods} seconds."
  metric_name         = "${var.application}-${var.environment}-FTP-retry-exceeded-DDBv2-large"
  statistic           = "Sum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.cw_alarm_ftp_threshold
  period              = var.cw_alarm_ftp_periods
  evaluation_periods  = var.cw_alarm_ftp_evaluation_periods
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.critical_alert_arn]
}

