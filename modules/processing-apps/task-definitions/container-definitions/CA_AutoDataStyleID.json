[{"name": "${task_friendly_name}", "image": "${image_url_name_tag}", "cpu": 13312, "memory": 56320, "environment": [{"name": "INI_BUCKET", "value": "${ini_bucket}"}, {"name": "ENVIRONMENT", "value": "${environment}"}, {"name": "COUNTRY", "value": "${country_iso_code}"}, {"name": "INI_S3_FILENAME", "value": "iniPackage.zip"}, {"name": "TASKNAME", "value": "${task_friendly_name}"}], "mountPoints": [{"sourceVolume": "aisdata", "containerPath": "/aisdata", "readonly": false}], "command": ["sudo -u \\#48 -g \\#48 INI_BUCKET=${ini_bucket} ENVIRONMENT=${environment} COUNTRY=${country_iso_code} INI_S3_FILENAME=iniPackage.zip TASKNAME=${task_friendly_name} /opt/rh/php55/root/usr/bin/php /data/git/AIS-1.0/NewSystem/site/cronjobs/AutoData/AutoDataStyleIDGet.php"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "${task_friendly_name}_${environment}", "awslogs-region": "${region}", "awslogs-stream-prefix": "ecstasks"}}}]