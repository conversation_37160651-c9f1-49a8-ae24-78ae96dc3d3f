###############################################################
# Pass-through Variables
###############################################################
variable "environment" {
}

###############################################################
# Cloudwatch Event Rule Variables
###############################################################

variable "task_friendly_name" {
  description = "The friendly name of the task definition"
}

variable "schedule_expression" {
  description = "The cron expression the scheduled task should use"
}

variable "role_arn" {
  description = "The role the event should use to run" # arn:aws:iam::131579720124:role/acct-managed/ais10-invoke-tasks
}

variable "task_arn" {
  description = "The arn of the task that will be run."
}

variable "cluster_arn" {
  description = "The arn of the ECS cluster the task should run in."
}

variable "task_count" {
  description = "The number of tasks that should run"
}

variable "enabled" {
  description = "Should the scheduled cloudwatch event be enabled"
  default     = false
}

