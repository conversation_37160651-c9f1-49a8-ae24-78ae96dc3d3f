<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>Rotating Auditing Queue</title>
<script type="text/javascript">

function locationHashChanged(){
 return window.location.hash != "" ? window.location.hash.replace("#", "") : "";
}
var env = locationHashChanged();
var prodDomain = "coxautoratesincentives.com", nonprodDomain = "coxais.com";
var domain = prodDomain;
window.onhashchange = function(){
	env = locationHashChanged();
	domain = prodDomain;
	if (env && env != "" && env != "production" && env != "prod"){
		domain = nonprodDomain;
		env = "-" + env;
	}
	setPage();
};

var pages = new Array(); // this will hold your pages
pages[0] = 'https://v1{env}.us.authoring.{domain}/auditing/StatusQueueBoard.php';
pages[1] = 'https://v1{env}.ca.authoring.{domain}/auditing/StatusQueueBoard.php';
pages[2] = 'https://v1{env}.us.authoring.{domain}/auditing/StatusQueueBoard.php';

var pageheaders = new Array(); // this will hold your pages
pageheaders[0] = 'us-lgflag80.gif';
pageheaders[1] = 'ca-lgflag80.gif';
pageheaders[2] = 'us-lgflag80.gif';

var ddbv2queue = new Array(); // this will hold your pages
ddbv2queue[0] = 'https://v1{env}.us.authoring.{domain}/auditing/ActiveDDBv2Monitor.php';
ddbv2queue[1] = 'https://v1{env}.ca.authoring.{domain}/auditing/ActiveDDBv2Monitor.php';
ddbv2queue[2] = 'https://v1{env}.us.authoring.{domain}/auditing/ActiveDDBv2Monitor.php';

var time = 45; // set this to the time you want it to rotate in seconds

// do not edit
var i = 1;
function setPage()
{
        if(i == pages.length)
        {
                i = 0;
        }
        document.getElementById('headholder1').setAttribute('src',pageheaders[i]);
        document.getElementById('headholder2').setAttribute('src',pageheaders[i]);
        document.getElementById('holder').setAttribute('src',pages[i].replace("{env}", env).replace("{domain}", domain));
        document.getElementById('footholder').setAttribute('src',ddbv2queue[i].replace("{env}", env).replace("{domain}", domain));
        i++;
}
setInterval("setPage()",time * 1000);
// do not edit
</script>
<style type="text/css">
html,body
    {
        margin: 0;
        padding: 0;
        border: 0;
        outline: 0;
        font-size: 90%;
        vertical-align: baseline;
    }
iframe,table
    {
        margin: 0;
        padding: 0 5px;
        border: 0;
        outline: 0;
        font-size: 90%;
        vertical-align: baseline;
    }
</style>
</head>

<body marginheight="0" marginwidth="0">
<TABLE cellspacing="0" cellpadding="0" valign="top" width="100%">
<tr>
<td>
<p align="left">
<iframe id="headholder1" name="CountryQueue1" height="100" width="165" align="left" scrolling="no" frameborder="0" src="us-lgflag80.gif"></iframe>
</p>
</td>
<td>
<p align="right">
<iframe id="headholder2" name="CountryQueue2" height="100" width="165" align="right" scrolling="no" frameborder="0" src="us-lgflag80.gif"></iframe>
</p>
</td>
</tr>
<tr>
<td colspan="2">
<iframe id="holder" name="StatusQueue" height="510" width="100%" scrolling="no" frameborder="0" src="https://v1.us.authoring.coxautoratesincentives.com/auditing/StatusQueueBoard.php" ></iframe>
<iframe id="footholder" name="DDBv2" height="120" width="99%" scrolling="no" frameborder="0" src="https://v1.us.authoring.coxautoratesincentives.com/auditing/ActiveDDBv2Monitor.php"></iframe>
</td>
</tr>
</TABLE>

</body>
</html>
