#==============================================================
# Pass Through Variables 
#==============================================================

variable "external_domain" {}
variable "external_hosted_zone_id" {}
variable "region" {}
variable "s3_website_hosted_zone_id" {}

#==============================================================
# Module Variables 
#==============================================================

variable "audit_queue_subdomain" {
  description = "The subdomain name for the audit queue."
  default     = "audit-queue"
}

variable "index_document" {
  description = "The index html document."
  default     = "AuditQueueRotate.html"
}

variable "index_document_content_type" {
  description = "The content type of the index html document."
  default     = "text/html"
}

variable "flag_us" {
  description = ".gif of the US flag."
  default     = "us-lgflag80.gif"
}

variable "flag_us_content_type" {
  description = "The content type of the flag_us."
  default     = "image/gif"
}

variable "flag_ca" {
  description = ".gif of the CA flag."
  default     = "ca-lgflag80.gif"
}

variable "flag_ca_content_type" {
  description = "The content type of the flag_ca."
  default     = "image/gif"
}
