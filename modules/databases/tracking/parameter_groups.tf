# MySql 8.0 parameter group
resource "aws_rds_cluster_parameter_group" "aurora_default_mysql_8" {
    name =  join(
    "-",
    compact([local.rds_component_name, "auroramysqleight"]),
  )
  family      = var.aurora_parameter_group_family_80
  description = "Parameter Group for the  Tracking Aurora cluster"
  parameter {
    name  = "log_bin_trust_function_creators"
    value = "1"
  }

  parameter {
    name  = "innodb_print_all_deadlocks"
    value = "1"
  }

  parameter {
    name  = "innodb_lock_wait_timeout"
    value = "120"
  }

  parameter {
    name  = "binlog_cache_size"
    value = "1048576"
  }

  parameter {
    name  = "key_buffer_size"
    value = "402653184"
  }

  parameter {
    name  = "max_allowed_packet"
    value = "16777216"
  }

  parameter {
    name  = "myisam_sort_buffer_size"
    value = "134217728"
  }

  parameter {
    name  = "max_heap_table_size"
    value = "67108864"
  }

  parameter {
    name  = "read_buffer_size"
    value = "2097152"
  }

  parameter {
    name  = "read_rnd_buffer_size"
    value = "16777216"
  }

  parameter {
    name  = "myisam_sort_buffer_size"
    value = "134217728"
  }

  parameter {
    name  = "tmp_table_size"
    value = "67108864"
  }

  parameter {
    name  = "max_connections"
    value = "500"
  }

  parameter {
    name  = "group_concat_max_len"
    value = "10240001"
  }

  parameter {
    name         = "lower_case_table_names"
    value        = "1"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "character_set_server"
    value        = "utf8"
    apply_method = "pending-reboot"
  }


  parameter {
    name         = "collation_server"
    value        = "utf8_general_ci"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "collation_connection"
    value        = "utf8_general_ci"
    apply_method = "pending-reboot"
  }
  
  tags = {
    Application  = var.application
    Service      = var.service
    Environment  = var.environment
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    SlackContact = var.slack_contact
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    Component    = var.component
    Name = join(
      "-",
      compact(
        [
         "dbpg-aurora", 
         var.region_abbreviated,
         var.application_abbreviated,
         var.service,
         var.environment,
         var.build_number
        ]
      )
    )
    Group = "DB"
  }
}
