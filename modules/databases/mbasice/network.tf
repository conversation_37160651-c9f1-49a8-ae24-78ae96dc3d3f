resource "aws_db_subnet_group" "default" {
  name = local.rds_component_name
  subnet_ids = var.private_subnet_ids

  tags = {
    Application  = var.application
    Environment  = var.environment
    Component    = var.component
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "dbsng-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
    Group        = "DB"
  }
}

