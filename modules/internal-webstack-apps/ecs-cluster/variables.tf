###############################################################
# Pass-through Variables
###############################################################

variable "application" {
}

variable "application_abbreviated" {
}

variable "service" {
}

variable "region" {
}

variable "country_iso_code" {
}

variable "region_abbreviated" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "homenet_cidr" {
}

variable "ais_cidr" {
}

variable "remote_cidr" {
}

variable "vpc_id" {
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "availability_zones" {
  type = list(string)
}

variable "efs_id" {
}

variable "component" {
}
variable "component_id" {
}

variable "nfs_cidr" {
}

###############################################################
# AutoScale Variables
###############################################################

variable "asg_min_size" {
  description = "Default min number of EC2s in autoscale group"
  default     = "1"
}

variable "asg_max_size" {
  description = "Default max number of EC2s in autoscale group"
  default     = "2"
}

variable "asg_desired_capacity" {
  description = "Default desired number of EC2s in autoscale group"
  default     = "1"
}

variable "asp_scale_out_adjustment" {
  description = "Instance side to use for 1.0 webstack"
  default     = "1"
}

variable "asp_scale_out_cooldown" {
  description = "Instance side to use for 1.0 webstack"
  default     = "60"
}

variable "asp_scale_in_adjustment" {
  description = "Instance side to use for 1.0 webstack"
  default     = "-1"
}

variable "asp_scale_in_cooldown" {
  description = "Instance side to use for 1.0 webstack"
  default     = "60"
}

variable "security_group" {
  description = "The security group the ec2 instances should use."
}

variable "asg_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for normal business hours"
}

variable "asg_scheduling_normal_map" {
  description = "Autoscaling scheduler map for normal business hour scale-up/down"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

variable "asg_extended_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for extended (change-day) business hours"
}

variable "asg_scheduling_extended_map" {
  description = "Autoscaling scheduler map for extended business hour scale-up/down on first/second of the month"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

###############################################################
# EC2 Variables
###############################################################

variable "asg_ami" {
  description = "AMI ID for the 1.0 webstack AMI pre-configured with the LAMP stack, code, and ini deployment"
}

variable "instance_type" {
  description = "Instance side to use for 1.0 webstack"
  default     = "m5.large"
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  default     = "AIS10"
}

###############################################################
# ECS Cluster Variables
###############################################################

variable "ecs_logging" {
  default     = "[\"json-file\",\"awslogs\"]"
  description = "Adding logging option to ECS that the Docker containers can use. It is possible to add fluentd as well"
}

###############################################################
# CloudWatch Variables
###############################################################

variable "cw_alarm_low_cpu_threshold" {
  description = "The threshold value to use on the Low CPU CloudWatch alarm."
  default     = "20"
}

variable "cw_alarm_low_cpu_period" {
  description = "The metric period value to use on the Low CPU CloudWatch alarm."
  default     = "300"
}

variable "cw_alarm_low_cpu_evaluation_periods" {
  description = "The evaluation periods value to use on the Low CPU CloudWatch alarm."
  default     = "40"
}

variable "cw_alarm_high_cpu_threshold" {
  description = "The threshold value to use on the High CPU CloudWatch alarm."
  default     = "40"
}

variable "cw_alarm_high_cpu_failsafe_threshold" {
  description = "The threshold value to use on the High CPU CloudWatch alarm."
  default     = "55"
}

variable "cw_alarm_high_cpu_period" {
  description = "The metric period value to use on the High CPU CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_cpu_evaluation_periods" {
  description = "The evaluation periods value to use on the High CPU CloudWatch alarm."
  default     = "2"
}

variable "cw_alarm_high_cpu_datapoint" {
  description = "number of data points on after which alarm should be triggerd"
  default     = "2"
}

variable "cw_alarm_high_cpu_failsafe_evaluation_periods" {
  description = "The evaluation periods value to use on the High CPU CloudWatch alarm."
  default     = "2"
}

variable "cw_alarm_high_cpu_failsafe_datapoint" {
  description = "number of data points on after which alarm should be triggerd"
  default     = "2"
}

variable "cw_alarm_low_mem_threshold" {
  description = "The threshold value to use on the Low Memory CloudWatch alarm."
  default     = "30"
}

variable "cw_alarm_low_mem_period" {
  description = "The metric period value to use on the Low Memory CloudWatch alarm."
  default     = "300"
}

variable "cw_alarm_low_mem_evaluation_periods" {
  description = "The evaluation periods value to use on the Low Memory CloudWatch alarm."
  default     = "40"
}

variable "cw_alarm_high_mem_threshold" {
  description = "The threshold value to use on the High Memory CloudWatch alarm."
  default     = "50"
}

variable "cw_alarm_high_mem_failsafe_threshold" {
  description = "The threshold value to use on the High Memory CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_mem_period" {
  description = "The metric period value to use on the High Memory CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_mem_evaluation_periods" {
  description = "The evaluation periods value to use on the High Memory CloudWatch alarm."
  default     = "2"
}

variable "cw_alarm_high_mem_failsafe_evaluation_periods" {
  description = "The evaluation periods value to use on the High Memory CloudWatch alarm."
  default     = "2"
}

variable "account_type" {
}

variable "account_id" {
}


variable "lambda_role_arn" {
  description = "Role that the lambda should run under"
  type        = map(string)

  default = {
    "nonprod" = "arn:aws:iam::************:role/acct-managed/ais10-ecs-termination-protection-lambda-role"
    "prod"    = "arn:aws:iam::************:role/acct-managed/ais10-ecs-termination-protection-lambda-role"
  }
}
