/* ##########################################################################################
  Create the CloudWatch alarm triggers for the autoscaling policies.
########################################################################################## */

/* Low CPU */
resource "aws_cloudwatch_metric_alarm" "low_cpu" {
  alarm_name = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-CPU-Low"

  alarm_description = "Triggered when CPU Reservation <= ${var.cw_alarm_low_cpu_threshold}% for ${var.cw_alarm_low_cpu_evaluation_periods} period(s) of ${var.cw_alarm_low_cpu_period} seconds."

  metric_name = "CPUReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = var.cw_alarm_low_cpu_threshold

  period             = var.cw_alarm_low_cpu_period
  evaluation_periods = var.cw_alarm_low_cpu_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.ecs_cluster_internal_webstack.name
  }

  alarm_actions = [aws_autoscaling_policy.default-scale-in.arn]
}

/* High CPU */
resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name        = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-CPU-High"
  alarm_description = "Triggered when CPU Reservation >= ${var.cw_alarm_high_cpu_threshold}% for ${var.cw_alarm_high_cpu_evaluation_periods} period(s) of ${var.cw_alarm_high_cpu_period} seconds."

  metric_name = "CPUReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.cw_alarm_high_cpu_threshold

  period              = var.cw_alarm_high_cpu_period
  evaluation_periods  = var.cw_alarm_high_cpu_evaluation_periods
  datapoints_to_alarm = var.cw_alarm_high_cpu_datapoint

  dimensions = {
    ClusterName = aws_ecs_cluster.ecs_cluster_internal_webstack.name
  }

  alarm_actions = [aws_autoscaling_policy.default-scale-out.arn]
}

/* High CPU Failsafe*/
resource "aws_cloudwatch_metric_alarm" "high_cpu_failsafe" {
  alarm_name        = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-CPU-High-Failsafe"
  alarm_description = "Failsafe Triggered when CPU Reservation >= ${var.cw_alarm_high_cpu_threshold}% for ${var.cw_alarm_high_cpu_failsafe_evaluation_periods} period(s) of ${var.cw_alarm_high_cpu_period} seconds."

  metric_name = "CPUReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.cw_alarm_high_cpu_failsafe_threshold

  period              = var.cw_alarm_high_cpu_period
  evaluation_periods  = var.cw_alarm_high_cpu_failsafe_evaluation_periods
  datapoints_to_alarm = var.cw_alarm_high_cpu_failsafe_datapoint

  dimensions = {
    ClusterName = aws_ecs_cluster.ecs_cluster_internal_webstack.name
  }

  alarm_actions = [aws_autoscaling_policy.default-scale-out.arn]
}

/* Low Memory Usage */
resource "aws_cloudwatch_metric_alarm" "low_mem" {
  alarm_name        = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-Mem-Low"
  alarm_description = "Triggered when ECS Memory Reservation <= ${var.cw_alarm_low_mem_threshold} for ${var.cw_alarm_low_mem_evaluation_periods} period(s) of ${var.cw_alarm_low_mem_period} seconds."

  metric_name = "MemoryReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = var.cw_alarm_low_mem_threshold

  period             = var.cw_alarm_low_mem_period
  evaluation_periods = var.cw_alarm_low_mem_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.ecs_cluster_internal_webstack.name
  }

  alarm_actions = [aws_autoscaling_policy.default-scale-in.arn]
}

/* High Memory Usage */
resource "aws_cloudwatch_metric_alarm" "high_mem" {
  alarm_name        = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-Mem-High"
  alarm_description = "Triggered when Memory Reservation >= ${var.cw_alarm_high_mem_threshold} for ${var.cw_alarm_high_mem_evaluation_periods} period(s) of ${var.cw_alarm_high_mem_period} seconds."

  metric_name = "MemoryReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.cw_alarm_high_mem_threshold

  period             = var.cw_alarm_high_mem_period
  evaluation_periods = var.cw_alarm_high_mem_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.ecs_cluster_internal_webstack.name
  }

  alarm_actions = [aws_autoscaling_policy.default-scale-out.arn]
}

/* High Memory Usage Failsafe */
resource "aws_cloudwatch_metric_alarm" "high_mem_failsafe" {
  alarm_name        = "${var.country_iso_code}-${var.application}-${var.environment}-${var.component}-Mem-High-Failsafe"
  alarm_description = "Failsafe Triggered when Memory Reservation >= ${var.cw_alarm_high_mem_failsafe_threshold} for ${var.cw_alarm_high_mem_evaluation_periods} period(s) of ${var.cw_alarm_high_mem_period} seconds."

  metric_name = "MemoryReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.cw_alarm_high_mem_failsafe_threshold

  period             = var.cw_alarm_high_mem_period
  evaluation_periods = var.cw_alarm_high_mem_failsafe_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.ecs_cluster_internal_webstack.name
  }

  alarm_actions = [aws_autoscaling_policy.default-scale-out.arn]
}
