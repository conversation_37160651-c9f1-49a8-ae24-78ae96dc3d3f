###############################################################
# Pass-through Variables
###############################################################
variable "application" {
}

variable "service" {
}

variable "region" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "component" {
}

variable "country_iso_code" {
}

###############################################################
# Task Definition Variables
###############################################################

variable "task_friendly_name" {
  description = "The friendly name of the task definition"
}

variable "container_definition_path" {
  description = "Path to the container definition json file"
}

variable "task_role_arn" {
  description = "The ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services."
}

variable "execution_role_arn" {
  description = "The Amazon Resource Name (ARN) of the task execution role that the Amazon ECS container agent and the Docker daemon can assume."
}

variable "requires_compatibilites" {
  description = "A set of launch types required by the task. The valid values are EC2 and FARGATE."
  default     = "EC2"
}

variable "image_url_name_tag" {
  description = "The ECR URL and image name:tag that should be used for the container."
}

variable "ini_bucket" {
  description = "The S3 bucket to grab the INI files from."
}

variable "rds_backup_bucket" {
  description = "The S3 bucket that nightly rds backups store the dump to."
  default     = ""
}

variable "rrri_topic_arn" {
  description = "RRR&I SNS Topic ARN"
  default     = ""
}

###############################################################
# Log Group Variables
###############################################################

variable "retention_in_days" {
  description = "Specifies the number of days you want to retain log events in the specified log group."
}

###############################################################
# Cloudwatch Event Rule Variables
###############################################################

variable "schedule_expression" {
  description = "The cron expression the scheduled task should use"
}

variable "event_rule_arn" {
  description = "The role the cloudwatch scheduled event should use to run"
}

variable "task_count" {
  description = "The number of tasks that should run"
  default     = 1
}

variable "enabled" {
  description = "Should the scheduled cloudwatch event be enabled"
  default     = false
}

###############################################################
# Alarm
###############################################################

variable "alarm_action_arn" {
  description = "ARN of the warning or critical (depending on severity of what's monitored) alert SNS topic"
}

variable "alarm_description" {
  description = "Description of the alarm that monitors the metric filter results"
  default     = ""
}

variable "alarm_metric_filter_pattern" {
  description = "Pattern for metric filter to identify logs that should trigger an alarm"
  default     = "\"error\" -\"memcache server\" -\"password\""
}

variable "application_name" {
}
