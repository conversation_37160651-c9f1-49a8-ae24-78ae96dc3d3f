# pass-through

variable "application" {
}

variable "service" {
}

variable "region" {
}

variable "account_id" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "component" {
}

variable "application_abbreviated" {
}

variable "region_abbreviated" {
}

variable "vpc_id" {
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "internal_hosted_zone_id" {
  description = "AWS Id of the ais-internal hosted zone"
}


variable "sns_warning_topic_arn" {
  description = "The ARN for the SNS Topic for Warning Notifications"
}

variable "sns_critical_topic_arn" {
  description = "The ARN for the SNS Topic for Critical Notifications"
}
variable "rds_db_name_suffix" {
  description = "The suffix to append to the DB name to create. Used to implement country name in database more flexibly (i.e. set to 'ca' for Canada)."
  default     = ""
}

variable "database_identifier" {
  description = "Our custom identifer for which database this represents"
  default     = "incentives"
}