resource "aws_ssm_parameter" "rrri_config_us" {
  name        = "/ais10/${var.environment}/us/rrri/config"
  description = "Allow DDBv2 process to lock the OEM for RRRI and publish SNS notification"
  type        = "String"
  value       = "{\"enabled\":false,\"allowNotification\":false}"

  tags = {
    environment = var.environment
  }

  lifecycle {
    ignore_changes = [value]
  }
}

resource "aws_ssm_parameter" "rrri_config_ca" {
  name        = "/ais10/${var.environment}/ca/rrri/config"
  description = "Allow DDBv2 process to lock the OEM for RRRI and publish SNS notification"
  type        = "String"
  value       = "{\"enabled\":false,\"allowNotification\":false}"

  tags = {
    environment = var.environment
  }

  lifecycle {
    ignore_changes = [value]
  }
}

