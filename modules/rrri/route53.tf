data "aws_rds_cluster" "aurora_primary" {
  cluster_identifier     = join(
    "-",
    compact(
      [
        "ais10",
        var.environment,
        "incentives-aurora-cluster",
      ]
    )
  )
}

locals {
  dns_prefix = join(
    "-",
    compact(
      [
        var.application,
        var.environment,
        var.database_identifier,
        var.rds_db_name_suffix,
        "aurora",
      ],
    ),
  )
}

resource "aws_route53_record" "reader" {
 
  zone_id = var.internal_hosted_zone_id
  name    = join("-", compact([local.dns_prefix, "reader"]))
  type    = "CNAME"
  ttl     = "300"
  records = [data.aws_rds_cluster.aurora_primary.reader_endpoint]
}
//	rrri-uat-beta-incentives-aurora-reader.ais-internal.com
