# Create the ECS Cluster
resource "aws_ecs_cluster" "ecs_cluster_consumer_webstack" {
  name = "${var.application}-${var.environment}-${var.component}-ecs"

  lifecycle {
    create_before_destroy = true
  }
}


data "aws_ecs_task_definition" "existing_task" {
  task_definition = "ConsumerWebstackApplication_${var.environment}"  # Replace with your actual task family name

}




resource "aws_ecs_service" "ecs-service" {
  name = "${var.application}-${var.environment}-${var.component}-service"
  cluster                = aws_ecs_cluster.ecs_cluster_consumer_webstack.arn
  launch_type            = "EC2"
  enable_execute_command = true

  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100
  desired_count                      = var.desired_task_count
  task_definition                    = "${data.aws_ecs_task_definition.existing_task.family}:${data.aws_ecs_task_definition.existing_task.revision}"

  load_balancer {
    target_group_arn = aws_lb_target_group.alb_tg.arn
    container_name   = "ConsumerWebstackApplication-${var.environment}"
    container_port   = 80
  }

  depends_on = [aws_lb_listener.alb-listener-http]
}

/*  Setup the security group for the EC2 instances / Autoscale Group. */
resource "aws_security_group" "ecs-sg" {
  name        = "${var.application}-${var.service}-${var.environment}-${var.component}-iw-apps-ec2"
  description = "Manages custom EC2 traffic."
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow SSH from in-network
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.homenet_cidr, var.remote_cidr, var.ais_cidr]
  }

  ingress {
    description = "https"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    security_groups = [var.security_group]
  }

  ingress {
    description = "http"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    security_groups = [var.security_group]
  }

ingress {
    description = "http"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [var.nfs_cidr]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "sg-ec2-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
    Component    = var.component
    component_id = var.component_id
  }
}

/* ##########################################################################################
  Create the Auto Scaling Group for the current deployment.
########################################################################################## */

resource "aws_autoscaling_group" "default" {
  name                 = "${var.application}-${var.environment}-${var.component}-asg"
  launch_configuration = aws_launch_configuration.default.name
  force_delete         = true

  min_size         = var.asg_min_size
  max_size         = var.asg_max_size
  desired_capacity = var.asg_desired_capacity

  wait_for_capacity_timeout = 0

  health_check_type         = "EC2"
  health_check_grace_period = 1000

  vpc_zone_identifier  = var.private_subnet_ids
  termination_policies = ["ClosestToNextInstanceHour", "Default"]
  enabled_metrics      = ["GroupMinSize", "GroupDesiredCapacity", "GroupInServiceInstances", "GroupPendingInstances", "GroupTerminatingInstances", "GroupTotalInstances"]

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [desired_capacity]
  }

  tag {
    key                 = "Name"
    value               = "ecs-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
    propagate_at_launch = true
  }

  tag {
    key                 = "Application"
    value               = var.application
    propagate_at_launch = true
  }

  tag {
    key                 = "Service"
    value               = var.service
    propagate_at_launch = true
  }

  tag {
    key                 = "Component"
    value               = var.component
    propagate_at_launch = true
  }

  tag {
    key                 = "Environment"
    value               = var.environment
    propagate_at_launch = true
  }

  tag {
    key                 = "Release"
    value               = var.build_number
    propagate_at_launch = true
  }

  tag {
    key                 = "LaunchedBy"
    value               = var.launched_by
    propagate_at_launch = true
  }

  tag {
    key                 = "LaunchedOn"
    value               = var.launched_on
    propagate_at_launch = true
  }

  tag {
    key                 = "SlackContact"
    value               = var.slack_contact
    propagate_at_launch = true
  }

  tag {
    key                 = "Cluster"
    value               = aws_ecs_cluster.ecs_cluster_consumer_webstack.name
    propagate_at_launch = true
  }

  tag {
    key                 = "AppStackTypeForSSM"
    value               = "ProcessingApps"
    propagate_at_launch = true
  }

  tag {
    key                 = "ssm-patch-installation"
    value               = "true"
    propagate_at_launch = true
  }
  tag {
    key                 = "Country-ISO-Code"
    value               = var.country_iso_code
    propagate_at_launch = true
  }

  tag {
    key                 = "coxauto:ci-id"
    value               = var.component_id
    propagate_at_launch = true
  }
}

# Default disk size for Docker is 22 gig, see http://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-optimized_AMI.html
resource "aws_launch_configuration" "default" {
  name_prefix          = "${var.application}-${var.environment}-${var.component}"
  image_id             = var.asg_ami
  instance_type        = var.instance_type
  security_groups      = [var.security_group, aws_security_group.ecs-sg.id]
  user_data            = data.template_file.user_data.rendered
  iam_instance_profile = "ais10-ec2-for-ec2ecs-role"
  key_name             = var.ec2_key_name

  # aws_launch_configuration can not be modified.
  # Therefore we use create_before_destroy so that a new modified aws_launch_configuration can be created
  # before the old one get's destroyed. That's why we use name_prefix instead of name.
  lifecycle {
    create_before_destroy = true
  }
}

data "template_file" "user_data" {
  template = file("${path.module}/userdata.tpl")
  vars = {
    ecs_cluster_name = aws_ecs_cluster.ecs_cluster_consumer_webstack.name
    ecs_logging      = var.ecs_logging
    ec2_environment  = var.environment
    region           = var.region
    efs_id           = var.efs_id
  }
}

