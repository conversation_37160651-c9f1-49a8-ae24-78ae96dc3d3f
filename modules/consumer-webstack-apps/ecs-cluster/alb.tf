# Creating application load balancer
resource "aws_lb" "application_load_balancer" {
  name            = "${var.application}-${var.environment}-cw-alb"
  internal           = false
  subnets         = [element(var.public_subnet_ids, 0), element(var.public_subnet_ids, 1), element(var.public_subnet_ids, 2)]
  security_groups = [aws_security_group.alb-sg.id]
}

# creating application load balancer target group
resource "aws_lb_target_group" "alb_tg" {
  name        = "${var.application}-${var.environment}-cw-alb-tg"
  port        = 80
  protocol    = "HTTP"
  vpc_id      = var.vpc_id
  target_type = "instance"

  health_check {
    healthy_threshold   = "2"
    interval            = "300"
    protocol            = "HTTP"
    matcher             = "200"
    timeout             = "120"
    path                = "/health-check.php"
    unhealthy_threshold = "5"
  }
  depends_on = [aws_lb.application_load_balancer]
}

resource "aws_lb_listener" "alb-listener-http" {
  load_balancer_arn = aws_lb.application_load_balancer.id
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
      host        = "#{host}"
      path        = "/#{path}"
      query       = "#{query}"
    }
  }
}

resource "aws_lb_listener" "alb-listener-https" {
  load_balancer_arn = aws_lb.application_load_balancer.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.alb_tg.arn
  }
}
