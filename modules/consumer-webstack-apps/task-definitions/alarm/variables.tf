###############################################################
# Pass-through Variables
###############################################################

variable "application" {
}

variable "service" {
}

variable "region" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "component" {
}

variable "name" {
}

variable "log_group_name" {
}

variable "alarm_action_arn" {
  description = "ARN of the warning or critical (depending on severity of what's monitored) alert SNS topic"
}

variable "alarm_description" {
  description = "Description of the alarm that monitors the metric filter results"
}

variable "metric_filter_pattern" {
  description = "Pattern for metric filter to identify logs that should trigger an alarm"
}

variable "metric_namespace" {
  description = "Namespace where metrics from log filters are stored"
  default     = "ProcessingApps"
}

