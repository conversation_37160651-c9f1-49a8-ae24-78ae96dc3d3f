#######################################
# Alarm Template used by all deployed tasks in container-definitions
######################################

resource "aws_cloudwatch_metric_alarm" "default_alarm" {
  alarm_name          = "TASK_${var.name}_${var.environment}_ERROR"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "${var.name}_${var.environment}_Errors"
  namespace           = var.metric_namespace
  period              = "300" # ten minutes in seconds
  statistic           = "Sum"
  threshold           = "1"
  alarm_description = var.alarm_description != "" ? var.alarm_description : format(
    "Warning, An Error has been detected in log %s_%s",
    var.name,
    var.environment,
  )
  alarm_actions = [var.alarm_action_arn]
  ok_actions    = [var.alarm_action_arn]
}

resource "aws_cloudwatch_log_metric_filter" "default_filter" {
  name    = "${var.name}_MetricFilter"
  pattern = var.metric_filter_pattern

  log_group_name = var.log_group_name

  metric_transformation {
    name      = "${var.name}_${var.environment}_Errors"
    namespace = var.metric_namespace
    value     = "1"
  }
}

