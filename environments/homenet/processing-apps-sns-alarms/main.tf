terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}


data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/sns-alerts"
    region = var.region
  }
}

#####################################################
# Call to Cloudwatch and SNS process
#####################################################
module "call-all-available-sns-alarms" {
  source = "../../../modules/processing-apps/sns-alarms"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  warning_alert_arn       = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
  critical_alert_arn      = data.terraform_remote_state.sns-alerts.outputs.critical_alert_arn
  rrri_critical_alert_arn = data.terraform_remote_state.sns-alerts.outputs.rrri_critical_alert_arn
}

