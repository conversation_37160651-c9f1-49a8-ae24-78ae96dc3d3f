terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.internal_webstack_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}
data "terraform_remote_state" "sns_alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/sns-alerts"
    region = var.region
  }
}

module "db_mbasice_57" {
  source = "../../../modules/databases/mbasice"

  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  region                  = var.region
  region_abbreviated      = var.regions_abbreviated[var.region]
  environment             = var.environment
  component               = var.component
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  vpc_id                  = data.aws_vpc.vpc.id
  nfs_cidr                = var.nfs_cidr
  ground_dc_cidrs         = var.ground_dc_cidrs
  private_subnet_ids      = data.aws_subnet_ids.private.ids
  internal_domain         = var.internal_domain
  internal_hosted_zone_id = var.internal_hosted_zone_id
  
  aurora_instance_class       = var.rds_cluster_instance_class_kinds[var.rds_instance_class_kind]
  rds_db_root_user            = var.rds_db_root_user
  rds_db_root_pw              = var.rds_db_root_pw
  rds_seed_from_snapshot_id   = var.custom_rds_snapshot_arn != "" ? var.custom_rds_snapshot_arn : var.use_most_recent_rds_snapshot == "true" ? var.rds_seed_from_snapshot_ids["mbasice"] : ""
  rds_multi_az                = var.rds_multi_az
  rds_skip_final_snapshot     = "true"
  rds_schedule_tag_value = lookup(
    var.rds_scheduling_map,
    var.environment,
    var.rds_scheduling_map["default"]
  )
  rds_backup_retention = var.rds_backup_retention

  sns_warning_topic_arn  = data.terraform_remote_state.sns_alerts.outputs.warning_alert_arn
  sns_critical_topic_arn = data.terraform_remote_state.sns_alerts.outputs.critical_alert_arn
}

