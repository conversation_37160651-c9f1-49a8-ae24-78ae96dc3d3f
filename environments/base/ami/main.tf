# Intentionally not setting up remote state for now since this is more of a manual process since the AMI rarely changes

terraform {
  required_providers {
    aws = {
      version = "2.70.1"
      source = "hashicorp/aws"
    }
  }
}

provider "aws" {
  region = var.region
}

module "ami" {
  source = "../../../modules/ami"

  # Environment and tagging
  account_type            = var.account_type
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  region                  = var.region
  region_abbreviated      = var.regions_abbreviated[var.region]
  environment             = var.environment
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Network
  vpc_name           = var.vpc_name
  vpc_id             = data.aws_vpc.vpc.id
  private_subnet_ids = data.aws_subnet_ids.private.ids

  # Image details
  base_ami_id           = var.base_ami_id
  user_data_script_name = var.user_data_folder_name

  # Available interpolation variables for user data scripts
  efs_id = var.efs_shares[var.region]
}

