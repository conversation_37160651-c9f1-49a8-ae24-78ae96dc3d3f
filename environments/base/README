What is the base environment?
==============================
This `base` environment builds the shared infrastructure that is used by all environments for this project within that account.
There isn't a way to deploy this at the moment since we've done it manually so far due to being a one-time thing.
This is basically a means of documenting what need to prepare/bootstrap an account.


What else does our account need?
=================================
As well as what is scripted out for the base environment, we also need to ensure the following resources exist:
1) TfState S3 Bucket (with versioning enabled): ais.nonprod|prod.infrastructure.tf.state
2) Hosted domain for cnames
