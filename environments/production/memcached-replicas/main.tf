terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.consumer_webstack_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}


data "terraform_remote_state" "master_db" {
  backend = "s3"

  config = {
    bucket = "ais.prod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/db-incentives"
    region = var.region
  }
}

data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.prod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/sns-alerts"
    region = var.region
  }
}


module "memcached_replicas_57" {
  source = "../../../modules/memcached-replicas"

  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  region                  = var.region
  region_abbreviated      = var.regions_abbreviated[var.region]
  environment             = var.environment
  component               = var.component
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  account_id              = var.account_id
  vpc_id                  = data.aws_vpc.vpc.id
  private_subnet_ids      = data.aws_subnet_ids.private.ids
  internal_hosted_zone    = var.internal_hosted_zone_id
  elasticache_node_type   = var.elasticache_node_type
  ec2_inbound_cidr        = data.aws_vpc.vpc.cidr_block
  warning_alert_arn  = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
  critical_alert_arn = data.terraform_remote_state.sns-alerts.outputs.critical_alert_arn
}
