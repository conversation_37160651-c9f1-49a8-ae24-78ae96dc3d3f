#==============================================================
# Production Consumer Webstack Variables
#
# This file imports shared variables and defines component-specific
# variables for the production consumer webstack environment.
#==============================================================

# Import shared global variables
# These are defined in shared-variables.global.tf
variable "regions_abbreviated" {
  description = "Mapping of AWS region names to their abbreviated forms for resource naming"
  type        = map(string)
}

variable "environment" {
  description = "Name of the environment we are building"
  type        = string
}

variable "build_number" {
  description = "Build number for tracking deployments and releases"
  type        = string
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string
}

variable "region" {
  description = "AWS Region where resources will be deployed"
  type        = string
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string
}

variable "application" {
  description = "Name of the application to be used when tagging AWS resources"
  type        = string
}

variable "application_abbreviated" {
  description = "Abbreviation for application name used in resource naming"
  type        = string
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
}

variable "slack_contact" {
  description = "Slack channel that should be notified by monitoring alerts"
  type        = string
}

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network"
  type        = string
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network"
  type        = string
}

variable "remote_cidr" {
  description = "The public CIDR block of the remote networks"
  type        = string
}

variable "consumer_webstack_component_id" {
  description = "The Component ID of the consumer-webstack for Cox Auto tracking"
  type        = string
}

# Import production-specific variables
# These are defined in shared-variables.prod.tf
variable "account_type" {
  description = "Type of AWS account"
  type        = string
}

variable "account_id" {
  description = "ID of AWS account"
  type        = string
}

variable "package_bucket_names" {
  description = "The S3 bucket that code and INI should be pulled from"
  type        = map(string)
}

variable "external_hosted_zone_id" {
  description = "AWS Id of the coxautoratesincentives.com hosted zone"
  type        = string
}

variable "webstack_ami_ids" {
  description = "AMI IDs for the Consumer and Internal Webstack"
  type        = map(string)
}

variable "alb_logs_bucket" {
  description = "S3 bucket to store the ALB logs"
  type        = string
}

variable "alb_logs_enabled" {
  description = "Should enable the ALB logs"
  type        = bool
}

# Component-specific variables
variable "nfs_cidr" {
  description = "CIDR block for NFS access"
  type        = string
  default     = "10.0.0.0/8"
}

variable "certificate_arn" {
  description = "ARN of the SSL certificate for the load balancer"
  type        = string
}

variable "availability_zones" {
  description = "List of availability zones to deploy resources in"
  type        = list(string)
}
