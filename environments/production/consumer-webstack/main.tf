#==============================================================
# Production Consumer Webstack Infrastructure
#
# This configuration deploys the consumer-facing web stack
# infrastructure for the production environment.
#==============================================================

terraform {
  # Terraform version constraints
  required_version = ">= 1.8.0, < 2.0.0"

  # Required providers with version constraints
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.83.0"
    }
  }

  # S3 backend configuration
  backend "s3" {
    # Backend configuration provided via backend config files
    # bucket = "ais.prod.{region}.infrastructure.tf.state"
    # key    = "AIS-1.0/production/consumer-webstack"
    # region = "{region}"
  }
}

# AWS Provider configuration with comprehensive tagging
provider "aws" {
  region = var.region

  # Default tags applied to all resources
  default_tags {
    tags = {
      # Cox Auto Component ID for tracking
      "coxauto:ci-id" = var.consumer_webstack_component_id

      # Standard resource tags
      Application  = var.application
      Service      = var.service
      Component    = var.component
      Environment  = var.environment
      Release      = var.build_number
      LaunchedBy   = var.launched_by
      LaunchedOn   = var.launched_on
      SlackContact = var.slack_contact

      # Terraform managed indicator
      ManagedBy = "terraform"

      # Infrastructure version
      InfrastructureVersion = "AIS-1.0"

      # Environment classification
      EnvironmentType = "production"
    }
  }

  # Ignore tags that are managed by other systems
  ignore_tags {
    key_prefixes = [
      "cai:catalog",
      "aws:",
      "kubernetes.io/",
      "k8s.io/"
    ]
  }
}

# Local values for resource naming and configuration
locals {
  # Common naming prefix for resources
  name_prefix = "${var.application_abbreviated}-${var.environment}-${var.component}"

  # Region abbreviation for resource naming
  region_abbr = var.regions_abbreviated[var.region]

  # Full resource name template
  resource_name_template = "${local.name_prefix}-${local.region_abbr}"

  # Environment-specific configuration
  is_production = true

  # S3 bucket naming for state and logs
  state_bucket_prefix = "ais.prod"

  # Common tags that can be merged with resource-specific tags
  common_tags = {
    Name         = local.resource_name_template
    Application  = var.application
    Service      = var.service
    Component    = var.component
    Environment  = var.environment
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    ManagedBy    = "terraform"
  }
}


data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.prod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/sns-alerts"
    region = var.region
  }
}

data "aws_wafv2_web_acl" "web_acl" {
  name  = "AIS-CAI-ERSManagedRules-consumer-production-regional-webacl"
  scope = "REGIONAL"
}
module "consumer_webstack" {
  source = "../../../modules/consumer-webstack"

  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  component               = var.component
  region                  = var.region
  region_abbreviated      = var.regions_abbreviated[var.region]
  environment             = var.environment
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  homenet_cidr            = var.homenet_cidr
  ais_cidr                = var.ais_cidr
  remote_cidr             = var.remote_cidr
  nfs_cidr                = var.nfs_cidr
  package_bucket_name     = var.package_bucket_names[var.region]
  country_iso_code        = "US"
  elb_route53_suffix      = ".us.consumer"
  alb_logs_bucket         = var.alb_logs_bucket
  alb_logs_enabled        = var.alb_logs_enabled
  external_hosted_zone_id = var.external_hosted_zone_id

  vpc_id             = data.aws_vpc.vpc.id
  certificate_arn    = var.certificate_arn
  public_subnet_ids  = data.aws_subnet_ids.public.ids
  private_subnet_ids = data.aws_subnet_ids.private.ids
  availability_zones = var.availability_zones

  asg_ami                = var.webstack_ami_ids[var.region]
  webstack_instance_type = "m5.large"
  asg_min_size           = "5"
  asg_max_size           = "10"
  asg_desired_capacity   = "5"
  asg_min_elb_capacity   = "5"

  asg_scheduling_enabled          = "false"
  asg_extended_scheduling_enabled = "false"
  waf_acl_arn_linux  = data.aws_wafv2_web_acl.web_acl.arn
  warning_alert_arn  = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
  critical_alert_arn = data.terraform_remote_state.sns-alerts.outputs.critical_alert_arn
  component_id = var.consumer_webstack_component_id
}

