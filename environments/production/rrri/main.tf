terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region
}

data "terraform_remote_state" "sns_alerts" {
  backend = "s3"

  config = {
    bucket = "ais.prod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/sns-alerts"
    region = var.region
  }
}

module "rrri" {
  source = "../../../modules/rrri"

  application   = "rrri"
  account_id    = var.account_id
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  application_abbreviated = "rrri"
  region_abbreviated      = var.regions_abbreviated[var.region]

  vpc_id             = data.aws_vpc.vpc.id
  private_subnet_ids = data.aws_subnet_ids.private.ids

  internal_hosted_zone_id = var.internal_hosted_zone_id
  sns_warning_topic_arn  = data.terraform_remote_state.sns_alerts.outputs.warning_alert_arn
  sns_critical_topic_arn = data.terraform_remote_state.sns_alerts.outputs.critical_alert_arn
}

