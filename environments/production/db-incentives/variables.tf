#==============================================================
# Component Variables 
#==============================================================

variable "custom_rds_snapshot_arn" {
  # Only to be used for fail-over events to allow restoring a snapshot in another region.
  # 
  # Incentives RDS component is different from the rest because it stamps out both a US and CA instance so we need two ARNs to seed. 
  # To avoid making major deployment changes at this time since incentives needs its own build to add a second variable like this one, 
  # the expected value format is "arn:us:yadda:etc,arn:ca:yadda:etc" where both ARNs are comma-separated with US first and CA second.

  description = "Custom RDS snapshot identifier as seed data"
  default     = ""
}