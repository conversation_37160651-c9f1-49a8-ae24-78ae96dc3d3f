terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.consumer_webstack_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/sns-alerts"
    region = var.region
  }
}

data "aws_wafv2_web_acl" "web_acl" {
  name  = "AIS-CAI-ERSManagedRules-consumer-nonprod-regional-webacl"
  scope = "REGIONAL"
}
module "consumer_webstack" {
  source = "../../../modules/consumer-webstack"

  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  component               = var.component
  region                  = var.region
  region_abbreviated      = var.regions_abbreviated[var.region]
  environment             = var.environment
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  homenet_cidr            = var.homenet_cidr
  ais_cidr                = var.ais_cidr
  remote_cidr             = var.remote_cidr
  nfs_cidr                = var.nfs_cidr
  package_bucket_name     = var.package_bucket_names[var.region]
  country_iso_code        = "US"
  elb_route53_suffix      = "-${var.environment}.us.consumer"
  alb_logs_bucket         = var.alb_logs_bucket
  alb_logs_enabled        = var.alb_logs_enabled
  external_hosted_zone_id = var.external_hosted_zone_id

  vpc_id             = data.aws_vpc.vpc.id
  certificate_arn    = var.certificate_arn
  public_subnet_ids  = data.aws_subnet_ids.public.ids
  private_subnet_ids = data.aws_subnet_ids.private.ids
  availability_zones = var.availability_zones
  asg_ami                 = var.webstack_ami_ids[var.region]

  asg_scheduling_enabled          = "true"
  asg_scheduling_normal_map       = var.autoscale_scheduling_normal_map
  asg_extended_scheduling_enabled = var.environment == "uat-alpha" || var.environment == "uat-beta" || var.environment == "uat-ext01" || var.environment == "uat-ext02" ? "true" : "false"
  asg_scheduling_extended_map     = var.autoscale_scheduling_extended_map

  warning_alert_arn  = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
  critical_alert_arn = data.terraform_remote_state.sns-alerts.outputs.critical_alert_arn
  component_id = var.consumer_webstack_component_id
  waf_acl_arn_linux  = data.aws_wafv2_web_acl.web_acl.arn
}

