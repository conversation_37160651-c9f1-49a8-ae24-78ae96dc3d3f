terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

data "terraform_remote_state" "ecs-cluster" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/processing-apps-cluster"
    region = var.region
  }
}

data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/sns-alerts"
    region = var.region
  }
}

data "terraform_remote_state" "rrri" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/rrri"
    region = var.region
  }
}

###################################################
# DDBV2 Division Processor Task
###################################################

module "ddbv2-division-processing-small" {
  source = "../../../modules/processing-apps/task-definitions/unscheduled-task"

  country_iso_code          = "" # This task gets country passed in via parent ddbv2 script
  task_friendly_name        = "DDBv2ProcessDivision_small"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/DDBv2ProcessDivision_small.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "ddbv2-division-processing-medium" {
  source = "../../../modules/processing-apps/task-definitions/unscheduled-task"

  country_iso_code          = "" # This task gets country passed in via parent ddbv2 script
  task_friendly_name        = "DDBv2ProcessDivision_medium"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/DDBv2ProcessDivision_medium.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "ddbv2-division-processing-large" {
  source = "../../../modules/processing-apps/task-definitions/unscheduled-task"

  country_iso_code          = "" # This task gets country passed in via parent ddbv2 script
  task_friendly_name        = "DDBv2ProcessDivision_large"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/DDBv2ProcessDivision_large.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

###################################################
# Misc US Tasks
###################################################

module "audit-data-points" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_AuditDataPoints"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_AuditDataPoints.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 12,18,03 * * ? *" #Run at the top of the hour at 8am, 2pm & 10pm
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "autodata-squish-vin" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_AutoDataSquishVin"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_AutoDataSquishVin.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "autodata-style-id" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_AutoDataStyleID"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_AutoDataStyleID.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "autodata-vehicle-import" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_AutoDataVehicleImport"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_AutoDataVehicleImport.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "dataone-described-vehicle-extract" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DataOneDescribedVehicleExtract"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_DataOneDescribedVehicleExtract.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "15 07 * * ? *" #Run at 3:15am every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "ddbv2-file-generation" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DDBv2FileGeneration"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_DDBv2FileGeneration.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/10 * * * ? *" #run every 10 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"
  rrri_topic_arn            = data.terraform_remote_state.rrri.outputs.topic_arn

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "ddbv2-queue-population" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DDBv2QueuePopulation"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_DDBv2QueuePopulation.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "30 05 * * ? *" #Run at 1:35AM every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "described-vehicle-extract" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DescribedVehicleExtract"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_DescribedVehicleExtract.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "45 01 ? * 7 *" #Run every Saturday at 9:45pm
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "finance-company-report" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_FinanceCompanyReport"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_FinanceCompanyReport.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "30 06 * * ? *" #Run every day at 2:30am
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "programs-about-to-expire" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_ProgramsAboutToExpire"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_ProgramsAboutToExpire.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "7 19 ? * 7 *" #Run every Sunday at 3:07pm
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "rds-MbasIce-backup-store-s3" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_MbasIce_RDSBackup"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_MbasIce_RDSBackup.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 * * ? *" #Run once a day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"
  rds_backup_bucket         = var.rds_backup_bucket_names[var.region]

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "rds-Incentives-backup-store-s3" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_Incentives_RDSBackup"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_Incentives_RDSBackup.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 05 * * ? *" #Run once a day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"
  rds_backup_bucket         = var.rds_backup_bucket_names[var.region]

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "rds-import-backup" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "Import_Database_Backup"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/Import_Database_Backup.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 05 * * ? *" #Run once a day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "rds-incentives-program-history-truncate" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_Program_History_Truncate"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/Program_History_Truncate.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 * * * ? *" #Run once an hour
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

###################################################
# Misc CA Tasks
###################################################

module "audit-data-points-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_AuditDataPoints"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_AuditDataPoints.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 12,18,03 * * ? *" #Run at the top of the hour at 8am, 2pm & 10pm
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "autodata-squish-vin-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_AutoDataSquishVin"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_AutoDataSquishVin.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "autodata-style-id-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_AutoDataStyleID"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_AutoDataStyleID.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "autodata-vehicle-import-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_AutoDataVehicleImport"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_AutoDataVehicleImport.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "ddbv2-file-generation-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_DDBv2FileGeneration"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_DDBv2FileGeneration.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/10 * * * ? *" #run every 10 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"
  rrri_topic_arn            = data.terraform_remote_state.rrri.outputs.topic_arn

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "ddbv2-queue-population-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_DDBv2QueuePopulation"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_DDBv2QueuePopulation.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "30 05 * * ? *" #Run at 1:35AM every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "finance-company-report-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_FinanceCompanyReport"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_FinanceCompanyReport.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "30 06 * * ? *" #Run every day at 2:30am
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "programs-about-to-expire-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_ProgramsAboutToExpire"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_ProgramsAboutToExpire.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "7 19 ? * 7 *" #Run every Sunday at 3:07pm
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "rds-Incentives-backup-store-s3-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_Incentives_RDSBackup"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_Incentives_RDSBackup.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 06 * * ? *" #Run once a day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"
  rds_backup_bucket         = var.rds_backup_bucket_names[var.region]

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "rds-incentives-program-history-truncate-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_Program_History_Truncate"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/Program_History_Truncate.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 * * * ? *" #Run once an hour
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

#####################################################
# New Tasks for Cloudwatch and SNS process
#####################################################

module "ddbv2-check-long-running-oems-us" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_DDBv2_Check_LongRunning_OEMs"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_Check_DDBv2_LongRunning_OEMs.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/5 * * * ? *" #Run every 5 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "ddbv2-check-long-running-oems-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_DDBv2_Check_LongRunning_OEMs"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CA_Check_DDBv2_LongRunning_OEMs.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/5 * * * ? *" #Run every 5 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

###################################################
# Chained AutoData Import Tasks
###################################################

module "autodata-chained-tasks-us" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_Chained_AutoData_Import_Tasks"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/Chained_AutoData_Import_Tasks.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "autodata-chained-tasks-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_Chained_AutoData_Import_Tasks"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/Chained_AutoData_Import_Tasks.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 4 1 1 ? 2218" #run on midnight on Janurary first 2218
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

###################################################
# Monitors
###################################################

module "check-out-of-sync-program-tables-us" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_CheckOutOfSyncProgramTables"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CheckOutOfSyncProgramTables.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/15 * * * ? *" # Run every 15 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  alarm_action_arn            = data.terraform_remote_state.sns-alerts.outputs.critical_alert_arn
  alarm_description           = "Monitors for any programs in US to be out of sync between its tables"
  alarm_metric_filter_pattern = "\"ERROR:\""

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component
  cluster_arn   = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled       = true
}

module "check-out-of-sync-program-tables-ca" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_CheckOutOfSyncProgramTables"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/CheckOutOfSyncProgramTables.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/15 * * * ? *" # Run every 15 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  alarm_action_arn            = data.terraform_remote_state.sns-alerts.outputs.critical_alert_arn
  alarm_description           = "Monitors for any programs in CA to be out of sync between its tables"
  alarm_metric_filter_pattern = "\"ERROR:\""

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component
  cluster_arn   = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled       = true
}

###################################################
# Tracking
###################################################

module "legacy-product-tracking" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_LegacyProductTracking"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_LegacyProductTracking.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/3 * * * ? *" #Run every 3 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "move-tracking-logs-definition" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_MoveTrackingLogs"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_MoveTrackingLogs.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "5/10 * * * ? *" #Every 10 minutes starting at 5 minutes after hour
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "tracking-product-tracking-counts-report" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_TrackingProductTrackingCountsReport"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_TrackingProductTrackingCountsReport.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "5 09 1 * ? *" #Run on the first of the month at 5:05am
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "copy_mbas_to_tracking_server-us" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_Copy_MBAS_To_Tracking_Server"
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/US_CopyMBASToTrackingServer.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0 10 * * ? *" #Run every day at 6am UTC
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  cluster_arn      = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
}

module "tracking-database-pruner" {
  source = "../../../modules/processing-apps/task-definitions"

  country_iso_code          = "US"                     # Not country specific but required here, plus setting INIs only exist under US
  task_friendly_name        = "TrackingDatabasePruner" # Not including US in name since it's technically not country specific
  container_definition_path = "../../../modules/processing-apps/task-definitions/container-definitions/TrackingDatabasePruner.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/30 * * * ? *" # Run twice an hour
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"

  alarm_action_arn            = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
  alarm_description           = "Monitors for failure trying to execute a query for pruning the tracking database"
  alarm_metric_filter_pattern = "\"ERROR:\""

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  cluster_arn = data.terraform_remote_state.ecs-cluster.outputs.cluster_arn
  enabled     = false
}

