terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source  = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.consumer_webstack_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/sns-alerts"
    region = var.region
  }
}

module "waf" {
  source = "../../../modules/waf"
  application = "Consumer"
  region = var.region
  limit = var.waf_request_limit
  environment = var.environment
  critical_alert_arn      = data.terraform_remote_state.sns-alerts.outputs.critical_alert_arn
}
