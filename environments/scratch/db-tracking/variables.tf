#==============================================================
# Component Variables 
#==============================================================

variable "custom_rds_snapshot_arn" {
  # Unlike the other instances which incur reads, tracking db has no option for use_most_recent_rds_snapshot since it's only
  # writes so developers don't need recent data; however, it could be useful to restore a specific snapshot for rare events.

  description = "Custom RDS snapshot identifier as seed data"
  default     = ""
}

variable "rds_instance_class_kind" {
  description = "Kind of RDS instance class that's assigned to a type of environment ('testing' or 'staging' to vary instance size)"
  default     = "testing"
}