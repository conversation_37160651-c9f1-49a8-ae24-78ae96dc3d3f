#!/bin/sh

# Parameters
# $1 : app to launch

# Environment variables
# $INI_BUCKET : ini bucket (same bucket used for service config packages)
# $ENVIRONMENT : environment
# $COUNTRY : country iso code
# $INI_S3_FILENAME : ini S3 file name
# $TASKNAME : Friendly name of task

# $PARENT_TASK : If this task is a child task, then $PARENT_TASK will be the task ARN of the parent task that spawned this one

if [ ! -z "${PARENT_TASK}" ]; then
    echo "============================"
    echo "This is a child task of the parent task ${PARENT_TASK}"
    echo "============================"
fi

if [ -z "${1}" ] || [ -z "${INI_BUCKET}" ] || [ -z "${ENVIRONMENT}" ] || [ -z "${COUNTRY}" ] || [ -z "${INI_S3_FILENAME}" ] || [ -z "${TASKNAME}" ]; then
    echo "The following parameters are all required (in order):"
    echo "App that should be launched"
    echo ""
    echo "The following environment variables are required:"
    echo "INI_BUCKET (INI and Service Config Bucket)"
    echo "ENVIRONMENT (Environment)"
    echo "COUNTRY (Country Iso Code)"
    echo "INI_S3_FILENAME (INI S3 File Name)"
    echo "TASKNAME (Friendly Task Name)"
    echo ""
    echo "Exiting with error code 1...."
    exit 1
fi


# Download the INIs
mkdir -p /download-inis
aws s3api get-object --bucket ${INI_BUCKET} --key ${ENVIRONMENT}/${APPLICATION_NAME}/${COUNTRY}/${INI_S3_FILENAME} "/download-inis/${INI_S3_FILENAME}"
unzip -o "/download-inis/${INI_S3_FILENAME}" -d /download-inis/package/
rsync -a /download-inis/package/ /data/git/AIS-1.0/
#rm -rf /download-inis

# Download and update the service configs
mkdir -p /download-service-configs
aws s3api get-object --bucket ${INI_BUCKET} --key ${ENVIRONMENT}/${APPLICATION_NAME}/serviceConfigPackage.zip /download-service-configs/serviceConfigPackage.zip
unzip -o /download-service-configs/serviceConfigPackage.zip -d /download-service-configs/service-configs

# Update environment variables inside configuration files
FILES=$(find /download-service-configs/service-configs/httpd -type f -name '*.conf')
for file in $FILES; do
    sed -i -e "s/{\\[environment-name\\]}/${ENVIRONMENT}/g" "$file"
    sed -i -e "s/{\\[country-name\\]}/${COUNTRY}/g" "$file"
    sed -i -e "s/{\\[stack-name\\]}/${appStackType}/g" "$file"
done

# Set up PHP & HTTPD paths
#PhpSessionBasePath="/var/lib/php/5.5"
#echo "Defaulting to / as HTTP/PHP base path for Amazon Linux"

#find /download-service-configs/service-configs -type f -print0 | xargs -0 sed -i "s~{PhpSessionBasePath}~${PhpSessionBasePath}~g"

# Ensure required directories exist with correct permissions
mkdir -p /aisdata/${ENVIRONMENT}/${COUNTRY}/netdata/php/${appStackType}/{sessions,wsdlcache}
find /aisdata/${ENVIRONMENT}/${COUNTRY}/netdata/php/ -type d -print0 | xargs -0 chown apache:apache
find /aisdata/${ENVIRONMENT}/${COUNTRY}/netdata/php/ -type d -print0 | xargs -0 chmod 755

# Copy HTTPD & PHP configuration files
cp -f /download-service-configs/service-configs/httpd/httpd.conf /etc/httpd/conf/httpd.conf
sed -i 's/{HttpdBasePath}//g' /etc/httpd/conf/httpd.conf
cp -f /download-service-configs/service-configs/httpd/projects.conf /etc/httpd/conf.d/projects.conf
cp -f /download-service-configs/service-configs/httpd/php55-php.conf /etc/httpd/conf.d/php55-php.conf
cp -f /download-service-configs/service-configs/php/php.ini /etc/php-5.5.conf/php.ini

# Cleanup
#rm -rf /download-service-configs
#touch /data/service_configs_complete


# Make sure rsyslog is running
service rsyslog start


# Reset owner/permissions after s3 zips are extracted to avoid carrying over permissioned from extracted contents
chown -R 48:48 /data/git/AIS-1.0/
find /data/git/AIS-1.0/ -type d -exec chmod 755 {} \;
find /data/git/AIS-1.0/ -type f -exec chmod 644 {} \;
find /data/git/AIS-1.0/ShellScripts -type f -name "*.sh" -exec chmod 744 {} \;

sleep 2
# Run the specific app
# Weird parsing if the command uses &&, ||, etc. Put it in a string first
echo "cmd: ${1} ${2} ${3} ${4} ${5} ${6} ${7} ${8} ${9}"
eval "${1} ${2} ${3} ${4} ${5} ${6} ${7} ${8} ${9}"

/usr/sbin/httpd -D FOREGROUND &
while true; do
  sleep 43800h
done