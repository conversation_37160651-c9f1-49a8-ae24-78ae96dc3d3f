# Processing App Image Creation

FROM  web-app-base:build
LABEL maintainer="<PERSON>"
LABEL author="<PERSON>"

RUN yum update -y

RUN echo "installing rsync"
# Empty the /data/git/AIS-1.0/ directory
RUN yum install -y rsync

# Empty the /data/git/AIS-1.0/ directory
RUN rm -rf /data/git/AIS-1.0/*

# Copy codebase zip into container
COPY ./codebasePackage.zip /data/git/AIS-1.0/

# Extract the zip
RUN unzip /data/git/AIS-1.0/codebasePackage.zip -d /data/git/AIS-1.0/

# Set owner/permissions (user/group 48)
RUN chown -R 48:48 /data/git/AIS-1.0/
RUN find /data/git/AIS-1.0/ -type d -exec chmod 755 {} \;
RUN find /data/git/AIS-1.0/ -type f -exec chmod 644 {} \;
RUN find /data/git/AIS-1.0/ShellScripts -type f -name "*.sh" -exec chmod 744 {} \;

# Delete the zip
RUN rm -f /data/git/AIS-1.0/codebasePackage.zip

# Copy the shell script to a higher root
COPY ./taskentry.sh /data/git/AIS-1.0/

# Fix the shell script
RUN dos2unix /data/git/AIS-1.0/taskentry.sh
RUN chmod 777 /data/git/AIS-1.0/taskentry.sh

# Set entry point
ENTRYPOINT ["/data/git/AIS-1.0/taskentry.sh"]