#==============================================================
# Shared Variables for All Accounts
#
# This file contains global variables used across all environments
# and components in the AIS 1.0 infrastructure.
#==============================================================

###############################################################
# Lookup Dictionaries
###############################################################

variable "regions_abbreviated" {
  description = "Mapping of AWS region names to their abbreviated forms for resource naming"
  type        = map(string)
  default = {
    "us-east-1" = "ue1"
    "us-west-2" = "uw2"
  }

  validation {
    condition = alltrue([
      for region in keys(var.regions_abbreviated) :
      can(regex("^us-(east|west)-[0-9]$", region))
    ])
    error_message = "Region keys must be valid AWS region names (e.g., us-east-1, us-west-2)."
  }
}

###############################################################
# Required Variables from Outside
###############################################################

variable "environment" {
  description = "Name of the environment we are building (e.g., production, qamain, qarapid)"
  type        = string

  validation {
    condition = can(regex("^[a-z0-9-]+$", var.environment))
    error_message = "Environment name must contain only lowercase letters, numbers, and hyphens."
  }
}

variable "build_number" {
  description = "Build number for tracking deployments and releases"
  type        = string

  validation {
    condition     = can(regex("^[0-9]+$", var.build_number))
    error_message = "Build number must be a numeric string."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string

  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built (ISO 8601 format)"
  type        = string

  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid timestamp in ISO 8601 format."
  }
}

variable "region" {
  description = "AWS Region where resources will be deployed"
  type        = string

  validation {
    condition = can(regex("^[a-z0-9-]+$", var.region)) && contains([
      "us-east-1", "us-west-2", "us-east-2", "us-west-1",
      "eu-west-1", "eu-central-1", "ap-southeast-1", "ap-northeast-1"
    ], var.region)
    error_message = "Region must be a valid AWS region."
  }
}

variable "component" {
  description = "Name of the component being deployed (e.g., consumer-webstack, internal-webstack)"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.component))
    error_message = "Component name must contain only lowercase letters, numbers, and hyphens."
  }
}

###############################################################
# Application Variables
###############################################################

variable "application" {
  description = "Name of the application to be used when tagging AWS resources"
  type        = string
  default     = "ais10"

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for application name used in resource naming"
  type        = string
  default     = "a10"

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application_abbreviated)) && length(var.application_abbreviated) <= 5
    error_message = "Application abbreviation must be 5 characters or less and contain only lowercase letters and numbers."
  }
}

variable "service" {
  description = "The service that this configuration builds (e.g., web, api, processing)"
  type        = string
  default     = "web"

  validation {
    condition     = contains(["web", "api", "processing", "database", "monitoring"], var.service)
    error_message = "Service must be one of: web, api, processing, database, monitoring."
  }
}

variable "slack_contact" {
  description = "Slack channel that should be notified by monitoring alerts"
  type        = string
  default     = "+ais-operations"

  validation {
    condition     = can(regex("^[+#@][a-z0-9-]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by alphanumeric characters and hyphens."
  }
}

###############################################################
# VPC Variables
###############################################################

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network for security group rules"
  type        = string
  default     = "************/32"

  validation {
    condition     = can(cidrhost(var.homenet_cidr, 0))
    error_message = "HomeNet CIDR must be a valid CIDR block."
  }
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network for security group rules"
  type        = string
  default     = "***********/32"

  validation {
    condition     = can(cidrhost(var.ais_cidr, 0))
    error_message = "AIS CIDR must be a valid CIDR block."
  }
}

variable "remote_cidr" {
  description = "The public CIDR block of the remote networks for security group rules"
  type        = string
  default     = "**************/32"

  validation {
    condition     = can(cidrhost(var.remote_cidr, 0))
    error_message = "Remote CIDR must be a valid CIDR block."
  }
}

variable "ground_dc_cidrs" {
  description = "List of CIDR blocks for ground data center networks"
  type        = list(string)
  default     = ["************/32", "**************/32", "************/24"]

  validation {
    condition = alltrue([
      for cidr in var.ground_dc_cidrs : can(cidrhost(cidr, 0))
    ])
    error_message = "All ground DC CIDRs must be valid CIDR blocks."
  }
}

###############################################################
# Route 53 Variables
###############################################################

variable "internal_domain" {
  description = "Domain to use when creating internal DNS records"
  type        = string
  default     = "ais-internal.com"

  validation {
    condition     = can(regex("^[a-z0-9.-]+\\.[a-z]{2,}$", var.internal_domain))
    error_message = "Internal domain must be a valid domain name."
  }
}

variable "s3_website_hosted_zone_id" {
  description = "The Amazon AWS hosted zone IDs for S3 website hosting by region"
  type        = map(string)
  default = {
    "us-east-1" = "Z3AQBSTGFYJSTF"
    "us-west-2" = "Z3BJ6K6RIION7M"
  }

  validation {
    condition = alltrue([
      for zone_id in values(var.s3_website_hosted_zone_id) :
      can(regex("^Z[A-Z0-9]+$", zone_id))
    ])
    error_message = "All hosted zone IDs must be valid AWS hosted zone IDs (start with Z followed by alphanumeric characters)."
  }
}

###############################################################
# Component ID Variables
# These are used for tagging and tracking resources in the Cox Auto system
###############################################################

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps for Cox Auto tracking"
  type        = string
  default     = "CI0934608"

  validation {
    condition     = can(regex("^CI[0-9]+$", var.processing_apps_component_id))
    error_message = "Component ID must start with 'CI' followed by numbers."
  }
}

variable "internal_webstack_component_id" {
  description = "The Component ID of the internal-webstack for Cox Auto tracking"
  type        = string
  default     = "CI0941858"

  validation {
    condition     = can(regex("^CI[0-9]+$", var.internal_webstack_component_id))
    error_message = "Component ID must start with 'CI' followed by numbers."
  }
}

variable "consumer_webstack_component_id" {
  description = "The Component ID of the consumer-webstack for Cox Auto tracking"
  type        = string
  default     = "CI0941859"

  validation {
    condition     = can(regex("^CI[0-9]+$", var.consumer_webstack_component_id))
    error_message = "Component ID must start with 'CI' followed by numbers."
  }
}
