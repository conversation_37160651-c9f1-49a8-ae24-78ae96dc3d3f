#==============================================================
# Shared Variables for Cross Regions in Production Account
#
# This file contains production-specific variables that are shared
# across all components and regions in the production environment.
#==============================================================

variable "account_type" {
  description = "Type of AWS account (production environment)"
  type        = string
  default     = "prod"

  validation {
    condition     = var.account_type == "prod"
    error_message = "Account type must be 'prod' for production environment."
  }
}

variable "account_id" {
  description = "ID of AWS production account"
  type        = string
  default     = "************"

  validation {
    condition     = can(regex("^[0-9]{12}$", var.account_id))
    error_message = "Account ID must be a 12-digit number."
  }
}

variable "account_name" {
  description = "Name of AWS production account"
  type        = string
  default     = "awsaaia"

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.account_name))
    error_message = "Account name must contain only lowercase letters and numbers."
  }
}

variable "hosted_zone_id" {
  description = "Route 53 HostedZoneID for production domain"
  type        = string
  default     = "ZEFD6MLBXET7G"

  validation {
    condition     = can(regex("^Z[A-Z0-9]+$", var.hosted_zone_id))
    error_message = "Hosted zone ID must be a valid AWS hosted zone ID (start with Z followed by alphanumeric characters)."
  }
}

variable "lambda_role_arn" {
  description = "IAM role ARN that Lambda functions should assume"
  type        = string
  default     = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"

  validation {
    condition     = can(regex("^arn:aws:iam::[0-9]{12}:role/.+$", var.lambda_role_arn))
    error_message = "Lambda role ARN must be a valid IAM role ARN."
  }
}

variable "efs_shares" {
  # WARNING: When you change EFS IDs, you must update the Consumer/Internal Webstack AMI which premounts it
  description = "EFS shares used as general storage for the entire application"
  type        = map(string)
  default = {
    "us-east-1" = "fs-3a46ea72"
    "us-west-2" = "fs-09b0699719831c715"
  }
}

variable "efs_security_group" {
  description = "The security group that has access to the efs"
  type        = map(string)
  default = {
    "us-east-1" = "sg-cc81d485"
    "us-west-2" = "sg-06b44f90c47143f7e"
  }
}

variable "package_bucket_names" {
  description = "The S3 bucket that code and INI should be pulled from"
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.application.packages.ue1"
    "us-west-2" = "ais.1-0.application.packages.uw2"
  }
}

variable "internal_hosted_zone_id" {
  description = "AWS Id of the ais-internal hosted zone"
  default     = "ZEFD6MLBXET7G"
}

variable "external_hosted_zone_id" {
  description = "AWS Id of the coxautoratesincentives.com hosted zone"
  default     = "Z1TDOYE3C1D175"
}

variable "external_domain" {
  description = "AWS domain of the coxautoratesincentives.com hosted zone"
  default     = "coxautoratesincentives.com"
}

variable "webstack_ami_ids" {
  description = "AMI IDs for the Consumer and Internal Webstack (<#Ais10AmiDefs#> )"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0b83439163648ce59"
    "us-west-2" = "ami-0256451d6a80ee5c0"
  }
}

####https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-optimized_AMI.html
variable "ecs_ami_ids" {
  description = "Region specific AMI IDs for ECS (amzn2-ami-ecs-hvm-2.0.20200115-x86_64-ebs)"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0fe5f366c083f59ca"
    "us-west-2" = "ami-00b9dff790e659df2"
  }
}

###############################################################
# RDS Variables
###############################################################

variable "rds_db_root_user" {
  description = "Username for the master DB user"
  default     = "aisadmin"
}

variable "rds_db_root_pw" {
  description = "Password for the master DB user. Note that this may show up in logs, and it will be stored in the state file."
  default     = "n8PIWhrlyFG52inew"
}

variable "rds_multi_az" {
  description = "AZ for all RDS instances"
  default     = "true"
}

variable "rds_backup_bucket_names" {
  description = "The S3 bucket that nightly rds backups store the dump to."
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.rds.backups.ue1"
    "us-west-2" = "ais.1-0.rds.backups.uw2"
  }
}

variable "rds_backup_retention" {
  description = "The days to retain backups for. Must be 1 or greater to be a source for a Read Replica."
  default     = "30"
}

variable "rds_cluster_instance_class_kinds" {
  description = "Kind of RDS instance class that's assigned to a type of environment"
  type        = map(string)
  default = {
    "large"    = "db.r5.large"
    "xlarge"    = "db.r5.xlarge"
  }
}

variable "rrri_db_cluster_instance_count" {
  description = "DB Cluster instance count (Pass more then 1 for multi AZ)"
  default     = "2"
}

variable "rrri_db_instance_type" {
  description = "Instance type of the database"
  default     = "db.r5.large"
}

variable "include_rrri_db_deploy" {
  description = "Determine if RRRI DB deployment is required. For non-production, it can be false"
  default     = true
}


variable "incentives_cluster_replica_count" {
  default = 4
  description = "DB Cluster instance count"
}

variable "incentives_cluster_replica_count_ca" {
  default = 1
  description = "DB Cluster instance count"
}

###############################################################
# ELB related Variables
###############################################################


variable "alb_logs_bucket" {
  description = "S3 bucket to store the ALB logs"
  default     = "ais-production-alb-logs"
}

variable "alb_logs_enabled" {
  description = "Should enable the ALB logs"
  default     = true
}

variable "waf_request_limit" {
  description = "Request limit for prod WAF"
  default     = "20000"
}

