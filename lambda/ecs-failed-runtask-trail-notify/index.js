'use strict';

const { SSMClient, GetParametersCommand } = require("@aws-sdk/client-ssm");
const { SQSClient, ReceiveMessageCommand, DeleteMessageBatchCommand } = require("@aws-sdk/client-sqs");

const SQS = new SQSClient({ region: process.env.Region, sqs: '2012-11-05' });
const SSM = new SSMClient({ region: process.env.Region, ssm: '2014-11-06' });
const Slack = require('slack-node');
const Polly = require('polly-js');
const _ = require('underscore');

exports.handler = async (event, context, callback) => {

	// Function to fetch multiple values under a shared path from parameter store in a single call.
	const getParametersFromBasePath = async (parameterPath, parameterNames) => {
		const parameters = parameterNames.map(name => parameterPath.concat(name));

		try {
			let request = { Names: parameters, WithDecryption: true };

			let response = await SSM.send(new GetParametersCommand(request));
			if (response.InvalidParameters.length) {
				throw new Error('Unable to find parameters: ' + response.InvalidParameters.join(', '));
			}

			let result = response.Parameters
				.map(parameter => {
					return { Name: parameter.Name.substring(parameterPath.length), Value: parameter.Value };
				})
				.reduce((parameterGroup, parameter) => {
					parameterGroup[parameter.Name] = parameter.Value;
					return parameterGroup;
				}, {});

			return result;
		}
		catch (err) {
			console.log(`${err.code || 'Exception'}: ${err.message}`);
			return null;
		}
	};

	// Function to send slack notification with event info
	const sendSlackNotification = async (messages) => {
		const slackChannelParameterName = 'channel_name';
		const slackWebhookParameterName = 'webhook_url';
		const slackAuthTokenParameterName = 'auth_token';

		const parameters = await getParametersFromBasePath('/all/slack/operations/', [slackChannelParameterName, slackWebhookParameterName, slackAuthTokenParameterName]);
		if (parameters === null)
			throw new Error('Could not retrieve Slack settings from SSM Parameter Store');

		return new Promise((resolve, reject) => {
			const slackClient = new Slack();
			slackClient.setWebhook(parameters[slackWebhookParameterName].concat(parameters[slackAuthTokenParameterName]));
			slackClient.webhook(
				{
					channel: parameters[slackChannelParameterName],
					username: "ECS Failed RunTask Trail Notifier",
					attachments: [
						{
							pretext: `Detected ECS task(s) that failed to start running in their cluster`,
							text: generateFailedTaskDetailMessage(messages),
							color: _.isString(process.env.Environment) && process.env.Environment.startsWith("prod") ? "#E23010" : "#EAEA00",
							mrkdwn_in: ['text']
						}
					]
				},
				function (err, response) {
					if (err) {
						reject(err);
					} else {
						resolve(response);
					}
				}
			);
		});
	};

	// Function to generate message details for notification
	const generateFailedTaskDetailMessage = (messages) => {
		const detailMessageParts = [];
		const failuresByClusterAndTask = _.groupBy(messages, msg => msg.clusterName + "#" + msg.taskName);

		for (let groupKey in failuresByClusterAndTask) {
			if (!failuresByClusterAndTask.hasOwnProperty(groupKey)) continue;

			let grouping = failuresByClusterAndTask[groupKey];
			detailMessageParts.push(`*Cluster: ${grouping[0].clusterName}*`);
			detailMessageParts.push(`*Task: ${grouping[0].taskName}*`);

			for (let i = 0, il = grouping.length; i < il; i++) {
				let eventUrl = `https://console.aws.amazon.com/cloudtrail/home?region=${process.env.Region}#/events?EventId=${grouping[i].eventId}`;
				let timestampLink = `<${eventUrl}|${grouping[i].timestamp}>`;
				let reasonsText = grouping[i].failures.reasons.join(', ');

				detailMessageParts.push(
					`${timestampLink}: Failed ${grouping[i].failures.failedNodes} of ${grouping[i].registeredNodes} registered nodes (${reasonsText})`);
			}

			detailMessageParts.push("\n"); // extra whitespace for task/cluster groups
		}

		return detailMessageParts.join("\n");
	};

	// Function to poll queue for a reasonable amount of messages per lambda execution
	const pollQueue = async (maxMessagesToNotify, consecutiveEmptyResponseLimit) => {
		const maxMessagesPerRequest = 10;
		const waitTimeSecondsPerRequest = 5;

		let receivedMessages = [];
		let consecutiveEmptyResponses = 0;

		// Poll for messages from queue
		do {
			let pollResponse = await Polly().waitAndRetry(3).executeForPromise(async function () {
				return await SQS.send(new ReceiveMessageCommand({
					QueueUrl: process.env.SqsQueueUrl,
					MaxNumberOfMessages: maxMessagesPerRequest,
					WaitTimeSeconds: waitTimeSecondsPerRequest,
					AttributeNames: []
				}));
			});

			if (_.isArray(pollResponse.Messages) && pollResponse.Messages.length > 0) {
				let messages = _.map(pollResponse.Messages, (msg) => {
					return { MessageId: msg.MessageId, ReceiptHandle: msg.ReceiptHandle, Body: msg.Body };
				});

				receivedMessages = receivedMessages.concat(messages);
				consecutiveEmptyResponses = 0;
			}
			else {
				consecutiveEmptyResponses++;
			}
		}
		while (receivedMessages.length <= maxMessagesToNotify && consecutiveEmptyResponses < consecutiveEmptyResponseLimit);

		// Batch delete received messages from queue
		if (receivedMessages.length) {
			let deletionEntries = _.map(receivedMessages, (msg) => { return { Id: msg.MessageId, ReceiptHandle: msg.ReceiptHandle }; });

			await Polly().waitAndRetry(3).executeForPromise(async function () {
				return await SQS.send(new DeleteMessageBatchCommand({ QueueUrl: process.env.SqsQueueUrl, Entries: deletionEntries }));
			});
		}

		return receivedMessages;
	};

	// Poll for messages in queue representing ECS RunTask failures 
	console.log("Polling for messages in queue");

	let failedTaskMessages = await pollQueue(50, 3);
	if (!failedTaskMessages.length) {
		console.log("No messages found in queue so quitting");
		return true;
	}

	// Send the notification 
	console.log(`Found ${failedTaskMessages.length} message(s) in queue so sending notification`);
	return sendSlackNotification(_.map(failedTaskMessages, (msg) => JSON.parse(msg.Body)));
};