'use strict';

console.log('Loading function');
const { AutoScalingClient, RecordLifecycleActionHeartbeatCommand, CompleteLifecycleActionCommand } = require("@aws-sdk/client-auto-scaling");
const { CloudWatchEventsClient, PutTargetsCommand, PutRuleCommand, DeleteRuleCommand, RemoveTargetsCommand, ListTargetsByRuleCommand, ListRulesCommand } = require("@aws-sdk/client-cloudwatch-events");
const { LambdaClient, GetPolicyCommand, RemovePermissionCommand, AddPermissionCommand } = require("@aws-sdk/client-lambda");
const { ECSClient, ListClustersCommand, ListContainerInstancesCommand, DescribeContainerInstancesCommand, UpdateContainerInstancesStateCommand } = require("@aws-sdk/client-ecs");

const asg = new AutoScalingClient({ region: process.env.Region });
const cloudwatchevents = new CloudWatchEventsClient({ region: process.env.Region });
const lambda = new LambdaClient({ region: process.env.Region });
const ecs = new ECSClient({ region: process.env.Region });

exports.handler = (event, context) => {
    var environment = process.env.Environment;
    var inputData = event.detail;
    console.log("current environment: ", environment);
    console.log("event", event);
    console.log("context", context);

    var startTerminationProtection = async () => {
        try {
            let ecsRequestForContainerList = await ecs.send(new ListClustersCommand({}));
            for (let i = 0; i < ecsRequestForContainerList.clusterArns.length; i++) {
                var clusterArn = ecsRequestForContainerList.clusterArns[i];
                if (clusterArn.includes(environment)) {
                    processContainerInstances(clusterArn);
                }
            }
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var processContainerInstances = async (clusterArn) => {
        try {
            var index = clusterArn.lastIndexOf("/");
            let clusterName = clusterArn.substring(index + 1, clusterArn.length);
            let response = await ecs.send(new ListContainerInstancesCommand({ cluster: clusterArn }));
            if (response.containerInstanceArns.length > 0) {
                var containerIds = response.containerInstanceArns.map(x => x.split('/').pop());
                await processInstances(containerIds, clusterName);
            }
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var processInstances = async (containerIds, clusterName) => {
        console.log(`processInstances method execution started for Cluster: ${clusterName}`);
        try {
            let containerInstances = await getContainerInstances(clusterName, containerIds);
            for (let i = 0; i < containerInstances.length; i++) {
                var isTerminatingEc2 = containerInstances[i].ec2InstanceId == inputData.EC2InstanceId;
                if (isTerminatingEc2) {
                    console.log(`Terminating instance found with EC2InstanceId: ${inputData.EC2InstanceId}`);
                    await rescheduleLambdaOrCompleteLC(containerInstances[i], clusterName);
                }
            }
            console.log(`processInstances method execution completed for Cluster: ${clusterName}`);
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var rescheduleLambdaOrCompleteLC = async (containerDetails, clusterName) => {
        try {
            let containerId = containerDetails.containerInstanceArn.split('/').pop();
            console.log(`Container Instance Id: ${containerId}, current state: ${containerDetails.status}`);
            if (containerDetails.status != 'DRAINING') {
                console.log(`Start setting up instance to DRAINING from ${containerDetails.status} state.`);
                await setInstanceToDraining(clusterName, containerId);
                console.log("Setting up instance to DRAINING complete.");
            }

            console.log("Getting container's latest details to check the pending/running tasks");
            let containerInstances = await getContainerInstances(clusterName, [containerId]);
            if (containerInstances.length > 0) {
                if (containerInstances[0].runningTasksCount > 0 || containerInstances[0].pendingTasksCount > 0) {
                    console.log(`Pending/Running task found: runningTasksCount=${containerInstances[0].runningTasksCount}, pendingTasksCount=${containerInstances[0].pendingTasksCount}`);
                    await recordLifecycleActionHeartbeat(inputData);
                    await scheduleNextLambdaExecution(inputData, context);
                }
                else {
                    console.log("No Running tasks found.");
                    await completeLifeCycleHook();
                    await deleteRuleFromCloudWatch(inputData.EC2InstanceId, context.functionName);
                }
            }
            else {
                console.log(`Error: Couldn't find the container instance details for Cluster: ${clusterName} and ContainerId: ${containerId}`);
            }
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var getContainerInstances = async (clusterName, containerIds) => {
        var params = {
            cluster: clusterName,
            containerInstances: containerIds
        };
        let response = await ecs.send(new DescribeContainerInstancesCommand(params));
        return response.containerInstances;
    };

    var recordLifecycleActionHeartbeat = async (inputData) => {
        console.log("RecordLifecycleActionHeartbeat method execution started");
        var params = {
            AutoScalingGroupName: inputData.AutoScalingGroupName,
            LifecycleActionToken: inputData.LifecycleActionToken,
            LifecycleHookName: inputData.LifecycleHookName,
            InstanceId: inputData.EC2InstanceId
        };
        try {
            await asg.send(new RecordLifecycleActionHeartbeatCommand(params));
            console.log("RecordLifecycleActionHeartbeat method execution completed");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var updateCloudWatchTargetData = async (ruleName, targetId, targetArn, targetInput) => {
        console.log("UpdateCloudWatchTargetData method execution started.");
        var params = {
            Rule: ruleName,
            Targets: [
                {
                    Id: targetId,
                    Arn: targetArn,
                    Input: targetInput
                }
            ]
        };
        try {
            await cloudwatchevents.send(new PutTargetsCommand(params));
            console.log("UpdateCloudWatchTargetData method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var updateCloudWatchRule = async (ruleName, ec2InstanceId, autoScalingGroupName, inputParameterModel) => {
        console.log("UpdateCloudWatchRule method execution started.");
        var params = {
            Name: ruleName,
            Description: `Terminate EC2 instance ${ec2InstanceId} from ${autoScalingGroupName}`,
            ScheduleExpression: "cron(0/10 * * * ? *)",
            State: "ENABLED"
        };
        try {
            let putRuleResponse = await cloudwatchevents.send(new PutRuleCommand(params));
            await updateCloudWatchTargetData(ruleName, context.functionName, context.invokedFunctionArn, JSON.stringify(event));
            await addPermissionIfNotExists(context.functionName, ruleName, putRuleResponse.RuleArn);
            console.log("UpdateCloudWatchRule method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var deleteRule = async (ruleName) => {
        console.log("DeleteRule method execution started.");
        var params = {
            Name: ruleName
        };
        try {
            await cloudwatchevents.send(new DeleteRuleCommand(params));
            console.log("DeleteRule method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var deleteRuleFromCloudWatch = async (ec2InstanceId, lambdaFunctionName) => {
        console.log(`Deleting rule for ${ec2InstanceId}, if exists...`);
        const ruleName = `Terminate-EC2-${ec2InstanceId}`;
        await getRulesNameAndDeleteRuleIfExists(ruleName);
        console.log(`Deleting rule for ${ec2InstanceId} is completed`);
    };

    var removeCloudWatchTarget = async (targetIds, ruleName) => {
        try {
            console.log("RemoveCloudWatchTarget method execution started.");
            if (targetIds.length > 0) {
                var params = {
                    Ids: targetIds.map(x => x.Id),
                    Rule: ruleName
                };
                await cloudwatchevents.send(new RemoveTargetsCommand(params));
            }
            await removePermissionIfExists(context.functionName, ruleName);
            console.log("RemoveCloudWatchTarget method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var getTargetsAndRemoveTargetsFromRule = async (ruleName) => {
        var params = {
            Rule: ruleName
        };
        console.log("GetTargetsAndRemoveTargetsFromRule method execution started.");
        try {
            let response = await cloudwatchevents.send(new ListTargetsByRuleCommand(params));
            console.log("RuleRemoved", response);
            await removeCloudWatchTarget(response.Targets, ruleName);
            console.log("GetTargetsAndRemoveTargetsFromRule method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var getRulesNameAndDeleteRuleIfExists = async (ruleName) => {
        var params = {
            NamePrefix: ruleName
        };
        try {
            let response = await cloudwatchevents.send(new ListRulesCommand(params));
            console.log("getRulesWithNamePrefix", response);
            console.log("response.Rules.length: ", response.Rules.length);
            if (response.Rules.length > 0) {
                await getTargetsAndRemoveTargetsFromRule(ruleName);
            }
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var getPolicy = async (functionName) => {
        console.log("GetPolicy method execution started.");
        var params = {
            FunctionName: functionName
        };
        try {
            let response = await lambda.send(new GetPolicyCommand(params));
            console.log("GetPolicy method execution complete.");
            return response;
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var removePermissionIfExists = async (functionName, statementId) => {
        console.log("RemovePermissionIfExists method execution started.");
        var params = {
            FunctionName: functionName,
            StatementId: statementId
        };
        try {
            let policyResponse = await getPolicy(functionName);
            var policyResult = JSON.parse(policyResponse.Policy);
            if (policyResult.Statement.some(x => x.Sid == statementId)) {
                try {
                    await lambda.send(new RemovePermissionCommand(params));
                }
                catch (err) {
                    console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
                }
            }
            await deleteRule(statementId);
            console.log("RemovePermissionIfExists method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var addPermissionIfNotExists = async (functionName, statementId, sourceArn) => {
        console.log(`Getting policy for Lambda ${functionName}`);
        try {
            var policyResponse = await getPolicy(functionName);
            var policyResult = JSON.parse(policyResponse.Policy);
            if (!policyResult.Statement.some(x => x.Sid == statementId)) {
                await addPermission(functionName, statementId, sourceArn);
            }
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var addPermission = async (functionName, statementId, sourceArn) => {
        console.log(`Adding permission to Lambda ${functionName} for ${statementId}`);
        var params = {
            Action: "lambda:InvokeFunction",
            FunctionName: functionName,
            Principal: "events.amazonaws.com",
            SourceArn: sourceArn,
            StatementId: statementId
        };
        try {
            await lambda.send(new AddPermissionCommand(params));
            console.log("AddPermission method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var scheduleNextLambdaExecution = async (inputParameterModel, context) => {
        const ruleName = `Terminate-EC2-${inputParameterModel.EC2InstanceId}`;
        await updateCloudWatchRule(ruleName, inputParameterModel.EC2InstanceId, inputParameterModel.AutoScalingGroupName, inputParameterModel);
    };

    var setInstanceToDraining = async (clusterName, containerId) => {
        console.log("SetInstanceToDraining method execution started.");
        try {
            var params = {
                containerInstances: [containerId],
                status: "DRAINING",
                cluster: clusterName
            };
            let response = await ecs.send(new UpdateContainerInstancesStateCommand(params));
            console.log("Instance set to draining:", response.containerInstances);
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    var completeLifeCycleHook = async () => {
        console.log("CompleteLifeCycleHook method execution started.");
        var params = {
            AutoScalingGroupName: inputData.AutoScalingGroupName,
            LifecycleActionResult: "CONTINUE",
            LifecycleActionToken: inputData.LifecycleActionToken,
            LifecycleHookName: inputData.LifecycleHookName,
            InstanceId: inputData.EC2InstanceId
        };
        try {
            await asg.send(new CompleteLifecycleActionCommand(params));
            console.log("CompleteLifeCycleHook method execution complete.");
        }
        catch (err) {
            console.log(`${err.code || 'Exception'}: ${err.message} ${'detail'} : ${err.stack}`);
            return;
        }
    };

    if (inputData.LifecycleTransition == "autoscaling:EC2_INSTANCE_TERMINATING") {
        console.log("Instance terminating event received");
        startTerminationProtection();
    }
    else {
        console.log("Exiting since the lifecycle transition is not EC2_INSTANCE_TERMINATING");
    }
};